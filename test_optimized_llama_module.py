"""
Test script for the optimized LLaMA module with comprehensive compound property extraction.
"""

import pandas as pd
from get_property_data_from_table_by_llama3_v4 import (
    convert_table_coordinates_in_list,
    get_llama_response,
    parse_llama_response,
    extract_data_to_dataframe
)

# Test data in the new JSON format as specified
prop_table_dict_list = [
    {
        'Young modulus': {
            'table_id': 'table_4_2', 
            'title': 'Tensile, impact and brittleness parameters for pristine poly(BA-a) and AC filled composites.', 
            'table_data': [
                {'0': 'Sample', '1': 'Tensile Stress', '2': '(MPa)', '3': 'Young modulus (GPa)', '4': 'Impact strength (kJ/m 2 )', '5': 'Brittleness ( B ) (% Pa/10 10 )', '6': 'Brittleness ( B ) (% Pa/10 10 )'}, 
                {'0': 'P(BA-a)', '1': '27.93 ± 0.78', '2': '27.93 ± 0.78', '3': '2.16 ± 0.122', '4': '1.4 ± 0.12', '5': '3.26', '6': '3.26'},
                {'0': 'P(BA-a/AC1)', '1': '32.49 ± 1.21', '2': '32.49 ± 1.21', '3': '2.56 ± 0.191', '4': '2.7 ± 0.12', '5': '2.74', '6': '2.74'},
                {'0': 'P(BA-a/AC2)', '1': '39.55 ± 1.53', '2': '39.55 ± 1.53', '3': '2.84 ± 0.182', '4': '3.9 ± 0.14', '5': '2.50', '6': '2.50'}
            ],
            'page_number': 4, 
            'coordinates': {'left': 42.0387077331543, 'top': 707.4191436767578, 'right': 562.8633422851562, 'bottom': 637.2298889160156, 'coord_origin': 'BOTTOMLEFT'}, 
            'page_size': {'width': 595.2760009765625, 'height': 793.7009887695312}
        }
    }
]

def test_coordinate_conversion():
    """Test coordinate conversion functionality"""
    print("=" * 60)
    print("TESTING COORDINATE CONVERSION")
    print("=" * 60)
    
    # Show original coordinates
    original_coords = prop_table_dict_list[0]['Young modulus']['coordinates']
    print(f"Original coordinates (BOTTOMLEFT): {original_coords}")
    
    # Convert coordinates
    converted_list = convert_table_coordinates_in_list(prop_table_dict_list)
    converted_coords = converted_list[0]['Young modulus']['coordinates']
    print(f"Converted coordinates (TOPLEFT): {converted_coords}")
    
    return converted_list

def test_json_structure():
    """Test JSON structure for LLaMA processing"""
    print("\n" + "=" * 60)
    print("TESTING JSON STRUCTURE FOR LLAMA")
    print("=" * 60)
    
    for prop_table_dict in prop_table_dict_list:
        for property_name, table_entry in prop_table_dict.items():
            print(f"\nProperty: {property_name}")
            print(f"Table ID: {table_entry.get('table_id')}")
            print(f"Title: {table_entry.get('title')[:80]}...")
            print(f"Number of data rows: {len(table_entry.get('table_data', []))}")
            
            # Show table structure
            table_data = table_entry.get('table_data', [])
            if table_data:
                print(f"Headers: {table_data[0]}")
                print(f"Sample data row: {table_data[1] if len(table_data) > 1 else 'No data'}")

def test_response_parsing():
    """Test response parsing functionality"""
    print("\n" + "=" * 60)
    print("TESTING RESPONSE PARSING")
    print("=" * 60)
    
    # Mock LLaMA response (simulating what the model would return)
    mock_response_text = '''[
        {"property_name": "Young modulus", "value": "2.16 ± 0.122", "unit": "GPa", "compound": "P(BA-a)"},
        {"property_name": "Young modulus", "value": "2.56 ± 0.191", "unit": "GPa", "compound": "P(BA-a/AC1)"},
        {"property_name": "Young modulus", "value": "2.84 ± 0.182", "unit": "GPa", "compound": "P(BA-a/AC2)"}
    ]'''
    
    print("Mock LLaMA response:")
    print(mock_response_text)
    
    # Parse the response
    parsed_data = parse_llama_response(mock_response_text, "Young modulus")
    print(f"\nParsed data ({len(parsed_data)} items):")
    for item in parsed_data:
        print(f"  {item}")
    
    return parsed_data

def test_dataframe_creation():
    """Test DataFrame creation with metadata"""
    print("\n" + "=" * 60)
    print("TESTING DATAFRAME CREATION")
    print("=" * 60)
    
    # Mock response data
    mock_response = {
        'Young modulus': [
            {'property_name': 'Young modulus', 'value': '2.16 ± 0.122', 'unit': 'GPa', 'compound': 'P(BA-a)'},
            {'property_name': 'Young modulus', 'value': '2.56 ± 0.191', 'unit': 'GPa', 'compound': 'P(BA-a/AC1)'},
            {'property_name': 'Young modulus', 'value': '2.84 ± 0.182', 'unit': 'GPa', 'compound': 'P(BA-a/AC2)'}
        ]
    }
    
    # Extract metadata
    table_metadata = {}
    for prop_table_dict in prop_table_dict_list:
        for property_name, table_entry in prop_table_dict.items():
            if isinstance(table_entry, dict):
                table_metadata[property_name] = table_entry
    
    print(f"Table metadata available for: {list(table_metadata.keys())}")
    
    # Create DataFrame
    df = extract_data_to_dataframe(mock_response, table_metadata)
    
    print(f"\nDataFrame shape: {df.shape}")
    print(f"DataFrame columns: {list(df.columns)}")
    print("\nDataFrame content:")
    print(df.to_string())
    
    return df

def show_expected_output_format():
    """Show the expected output format"""
    print("\n" + "=" * 60)
    print("EXPECTED OUTPUT FORMAT")
    print("=" * 60)
    
    expected_columns = [
        'property_name', 'value', 'unit', 'compound', 'source_key',
        'table_id', 'page_number', 'coord_left', 'coord_top', 'coord_right', 
        'coord_bottom', 'coord_origin', 'page_width', 'page_height'
    ]
    
    print("Expected DataFrame columns:")
    for i, col in enumerate(expected_columns, 1):
        print(f"  {i:2d}. {col}")
    
    print(f"\nTotal expected columns: {len(expected_columns)}")

if __name__ == "__main__":
    print("TESTING OPTIMIZED LLAMA MODULE")
    print("=" * 60)
    print("Testing comprehensive compound property extraction with:")
    print("- Direct JSON processing")
    print("- FITZ coordinate conversion") 
    print("- Complete metadata preservation")
    print("- Robust response parsing")
    
    # Run all tests
    converted_list = test_coordinate_conversion()
    test_json_structure()
    parsed_data = test_response_parsing()
    df = test_dataframe_creation()
    show_expected_output_format()
    
    print("\n" + "=" * 60)
    print("ALL TESTS COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    print("\nKey improvements in optimized module:")
    print("✅ Direct JSON input to LLaMA (no string conversion)")
    print("✅ Comprehensive prompts with examples")
    print("✅ Robust compound name extraction")
    print("✅ FITZ coordinate conversion")
    print("✅ Complete metadata preservation")
    print("✅ Error handling and validation")
    print("✅ Optimized DataFrame creation")
