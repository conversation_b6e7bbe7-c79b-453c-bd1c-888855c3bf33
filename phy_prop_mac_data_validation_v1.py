"""
This module provides functionality for normalizing and validating physical property.

It includes functions for standardizing units and potentially other data validation
 and normalization operations related to physical properties and it's characteristics.

The module is designed to work with pandas DataFrames, processing and cleaning data
to ensure consistency and accuracy in scientific and engineering applications.

Date 22-08-24
@author: <PERSON>
"""

import re
import numpy as np
import pandas as pd
import logging




def normalize_units(df):
    """
    Normalize the units in the 'units' column of the input DataFrame to '°C'.

    This function processes the 'units' column of the input DataFrame, standardizing
    various representations of Celsius to the uniform '°C' format. It handles different
    variations and potential typos in unit representation.

    Args:
    df : pandas.DataFrame
        The input DataFrame containing the 'units' column to be normalized.
    Returns:
    pandas.DataFrame
        The DataFrame with the 'units' column normalized.
    """

    try:
        # regex patterns to match various forms of Celsius and related variations
        celsius_patterns = [
            r'°C:*',
            r'°C,*',
            r'oC.*',
            r' C*',
            r'�C',
            r'°C[:;,,.)]*',
            r'\s*C[;,.)]*',
            r'\boC[;,.)]*',
            r'[^\d]C\b',
            r'1C[.;:)]?',
            r'8C[.;:)]?',
            r'\bK\b[.;:,]*',
            r'\?',
            r'\s+'
        ]

         # Combine the patterns into one regex pattern
        combined_pattern = '|'.join(celsius_patterns)

        # Define a function to normalize units to "°C"
        def normalize_units_to_celsius(units):
            if pd.notna(units):
                # Replace all matches with '°C'
                normalized = re.sub(combined_pattern, '°C', units.strip(), flags=re.IGNORECASE)
                
                # Remove trailing punctuation
                normalized = re.sub(r'[.,;:]+$', '', normalized)
                
                return normalized.strip()
            return units

        # Apply the function to the "units" column of the DataFrame
        df['units'] = df['units'].apply(normalize_units_to_celsius)
        
        # Return the final DataFrame with all columns intact
        return df
    
    except Exception as error:
        logging.error("normalize_units Error: %s", str(error))
        return df



def process_value_column(df):
    """
    Process the 'value_cond_value' column of the input DataFrame.

    This function cleans and standardizes the values in the 'value_cond_value' column
    by removing unwanted characters, replacing specific symbols, and formatting the data.
    It also filters out rows that don't contain numerical values.

    Args:
    df : pandas.DataFrame
        The input DataFrame containing the 'value_cond_value' column to be processed.

    Returns:
    pandas.DataFrame
        The processed DataFrame with cleaned 'value_cond_value' column and filtered rows.

    """

    try:
        # Define a function to process the "value_cond_value" column
        def clean_value(value):
            if pd.isna(value):
                return value

            # Convert to string and strip spaces
            value = str(value).strip()

            # Replace hyphens between two numbers with "|"
            value = re.sub(r'(?<=\d)[\-–—](?=\d)', '|', value)

            # Replace ":" with ""
            value = value.replace(':', '')

            # Replace "," with ""
            value = value.replace(',', '')

            # Replace "(" and ")" with ""
            value = value.replace('(', '').replace(')', '')

            # Replace "°" and "&" with ""
            value = value.replace('°', '').replace('&', '')

            # Replace "∼" with "|" if in between two numbers
            value = re.sub(r'(?<=\d)∼(?=\d)', '|', value)

            # Replace "∼" with "-" if starting the string and before a number
            value = re.sub(r'^∼(?=\d)', '-', value)

            # Replace "e" with "|" if in between two numbers
            value = re.sub(r'(?<=\d)e(?=\d)', '|', value)

            return value

        # Apply the cleaning function to the "value_cond_value" column
        df['value_cond_value'] = df['value_cond_value'].apply(clean_value)

        # Filter out rows that contain only alphabets and no numbers
        df = df[~df['value_cond_value'].str.fullmatch(r'[A-Za-z]+', na=False)]

        # Keep rows that contain numeric values, allowing patterns like "123|345"
        df = df[df['value_cond_value'].str.contains(r'\d', na=False)]

        # Return the final DataFrame with all columns intact
        return df

    except Exception as error:
        logging.error("process_value_column Error: %s", str(error))
        return df



def update_tags_based_on_magnetic_moment(df):
    """
    Update the 'TAGS' column based on the 'value_cond_value' for the 'Magnetic Moment' in the 'property_name' column.

    If any part of 'value_cond_value' > 50, set 'TAGS' to 'FALSE'.
    If all parts of 'value_cond_value' <= 50, set 'TAGS' to 'TRUE'.

    Args:
    - df (pd.DataFrame): Input DataFrame containing 'property_name', 'value_cond_value', and 'TAGS' columns.

    Returns:
    - pd.DataFrame: The updated DataFrame with modified 'TAGS' values.
    """

    def check_magnetic_moment(value):
        try:
            # Split the value by "|" and convert each part to numeric
            parts = [float(x) for x in value.split('|')]
            # If any part is greater than 50, return 'FALSE'
            if any(part > 50 for part in parts):
                return 'FALSE'
            # If all parts are less than or equal to 50, return 'TRUE'
            return 'TRUE'
        except ValueError:
            # If conversion fails, keep the original TAGS value
            return df['TAGS']

    # Apply the conditions to update the 'TAGS' column only for 'Magnetic Moment'
    condition_magnetic_moment = df['property_name'].str.strip().str.lower() == 'magnetic moment'
    df.loc[condition_magnetic_moment, 'TAGS'] = df.loc[condition_magnetic_moment, 'value_cond_value'].apply(check_magnetic_moment)

    return df


# Dictionary to map superscripts and subscripts to normal characters
superscripts_subscripts_map = {
    '¹': '1', '²': '2', '³': '3', '⁴': '4', '⁵': '5', '⁶': '6', '⁷': '7', '⁸': '8', '⁹': '9', '⁰': '0',
    '₁': '1', '₂': '2', '₃': '3', '₄': '4', '₅': '5', '₆': '6', '₇': '7', '₈': '8', '₉': '9', '₀': '0',
    'μ': 'μ'
}

def replace_superscripts_and_subscripts(s):
    """Replace superscript and subscript characters with normal ones."""
    for key, value in superscripts_subscripts_map.items():
        s = s.replace(key, value)
    return s



def clean_string(s):
    """Remove special characters, spaces, and convert to lowercase"""
    s = str(s).lower()  
    s = s.replace('¡', '-').replace(",...", "").replace(",", "").replace(";", "").replace(":", "").replace("/,", "").replace(",no_data", "").replace("°C,", "°C")
    s = re.sub(r'[°º]', '°', s) 
    s = replace_superscripts_and_subscripts(s)  

    # Remove special characters and spaces
    s = re.sub(r'[:;,.\s()\[\]{}!ÃÂ¡�?@#$^&*•º_=+⁄/–∙-]', '', s)

    return s



def tag_units_with_regex_for_all_rows(mac_data, property_df):
    """
    Tag units with regex for all rows in the MAC data.

    This function processes the MAC data and property data to tag units using regex patterns.
    It cleans and standardizes property names and units, merges the datasets, and applies
    unit validation based on predefined patterns.

    Args:
        mac_data (pd.DataFrame): The MAC data containing property information.
        property_df (pd.DataFrame): The property data containing unit information.

    Returns:
        pd.DataFrame: The processed MAC data with tagged units and updated 'TAGS' column.
    """

    try:

        def clean_unit_list(unit_list):
            if pd.notna(unit_list):
                units = [unit.strip().lower() for unit in unit_list.split(',')]
                # Remove empty strings
                return [unit for unit in units if unit]  
            return []

        def clean_units(units):
            if pd.notna(units) and units.strip() != '':
                return units.strip().lower()  
            return np.nan 
        
        mac_data.drop_duplicates(inplace=True)

        mac_data = normalize_units(mac_data)

        mac_data = process_value_column(mac_data)

        mac_data = update_tags_based_on_magnetic_moment(mac_data)

        # Clean the 'property_name' column in both DataFrames for case-insensitive matching
        mac_data['cleaned_property_name'] = mac_data['property_name'].apply(clean_string)
        property_df['cleaned_property_name'] = property_df['property_name'].apply(clean_string)

        # Clean the 'unit_list' column in property_df
        property_df['unit_list'] = property_df['unit_list'].apply(clean_unit_list)

        def clean_and_split_property_names(property_name):
            if pd.notna(property_name):
                properties = [clean_string(prop.strip()) for prop in property_name.split('|')]

                # Return the list of cleaned properties
                return properties  
            return []

        mac_data['cleaned_property_names'] = mac_data['property_name'].apply(clean_and_split_property_names)
        mac_data['cleaned_units'] = mac_data['units'].apply(clean_units)

        # Merge mac_data with property_df on 'cleaned_property_name'
   
        # Drop duplicates in property_df based on 'cleaned_property_name'
        property_df_unique = property_df.drop_duplicates(subset=['cleaned_property_name'])

        # Merge mac_data with the unique property_df
        merged_data = mac_data.merge(property_df_unique[['cleaned_property_name', 'unit_list']], on='cleaned_property_name', how='left')
 
        excluded_properties = [
        "dielectricalconstant", "κ", "ɛ", "εs", "Ɛr", "εr", "relativepermittivity", 
        "dielectricconstant", "dielectricpermittivity", "εmax", "dielectricparameters", 
        "refractiveindex", "nd", "n20d", "refractionindex", "indexofrefraction", 
        "refractiveindices", "pka", "aciddisociationconstant", "associationconstantofprotanation", 
        "associationconstant", "aciddissociationconstant", "protanationconstant", "pkb", 
        "basedissociationconstant", "dielectricconstant", 
        ]

        def check_unit_with_regex(row):
            cleaned_units = row['cleaned_units']
            cleaned_property_name = row['cleaned_property_name']
            unit_list = row['unit_list']
            value_cond_value = row['value_cond_value']

            # Check for zero values in value_cond_value
            if pd.notna(value_cond_value) and str(value_cond_value).strip() in ["0", "0.0", "0.00", "0.000", "0.0000", "0.00000", "0.000000", "0.0000000"]:
                return "FALSE"
            
            if cleaned_property_name.lower() in excluded_properties:
                return "SKIPPED"

            if pd.isna(cleaned_units):
                return "FALSE"

            if unit_list and isinstance(unit_list, list):
                for unit in unit_list:
                    if cleaned_units == unit:
                        return "TRUE"
                    
            return "FALSE"

        merged_data['TAGS'] = merged_data.apply(lambda row: check_unit_with_regex(row), axis=1)
        
        merged_data = merged_data.drop(columns=['cleaned_units', 'unit_list', 'cleaned_property_name', 'cleaned_property_names'])
        return merged_data
    except Exception as error:
        logging.error("select_property_and_replace Error: %s", str(error))
        return pd.DataFrame()



def select_property_and_replace(df, property_unit_file):
    """
    Select and replace property names in the input DataFrame based on a property-unit file.

    This function processes the input DataFrame by matching property names with units
    from a provided property-unit file. It searches for units within 10 characters after
    the property symbol in the 'com_sentence' column and updates the 'property_name'
    column accordingly.

    Args:
        df (pandas.DataFrame): Input DataFrame containing columns 'property_name',
                               'prop_no_name', and 'com_sentence'.
        property_unit_file (str): Path to the Excel file containing property-unit mappings.

    Returns:
        pandas.DataFrame: Processed DataFrame with updated 'property_name' column.
                          Returns an empty DataFrame if an error occurs.
    """

    try:
        # Load the property-unit file
        property_units = pd.read_excel(property_unit_file, dtype=str)

        # Convert unit_list to string and handle NaN values, keeping empty strings intact
        property_units['unit_list'] = property_units['unit_list'].fillna('').astype(str)

        # Strip any whitespace from all the data in property_units and split unit_list into lists
        property_units['unit_list'] = property_units['unit_list'].apply(lambda x: [unit.strip() for unit in x.split(',') if unit])
        property_units['property_name'] = property_units['property_name'].str.strip().str.lower()

        # Convert property_name, prop_no_name, and com_sentence to string and keep empty strings intact in df
        df['property_name'] = df['property_name'].fillna('').astype(str)
        df['prop_no_name'] = df['prop_no_name'].fillna('').astype(str)
        df['com_sentence'] = df['com_sentence'].fillna('').astype(str)

        # Function to search for the unit within 10 characters after the property symbol
        def find_matching_property(row):
            properties = [p.strip().lower() for p in row['property_name'].split('|') if p.strip()]
            prop_no_name = row['prop_no_name'].strip()
            com_sentence = row['com_sentence']

            # Iterate through the split property names
            for prop in properties:
                if prop in property_units['property_name'].values:
                    # Get the corresponding unit list
                    unit_list = property_units[property_units['property_name'] == prop]['unit_list'].values[0]

                    # Check for each unit within 10 characters after the property symbol
                    for unit in unit_list:

                        # Find the position of the property symbol in com_sentence
                        prop_pos = com_sentence.find(prop_no_name)
                        if prop_pos != -1:

                            # Get the text within 10 characters after the property symbol
                            text_after_symbol = com_sentence[prop_pos + len(prop_no_name): prop_pos + len(prop_no_name) + 10]
                            
                            # Check if the unit is in this text
                            if re.search(re.escape(unit), text_after_symbol):
                                return prop.capitalize()

            # If no match is found, return the first property as default
            return properties[0].capitalize() if properties else row['property_name']

        # Apply the function to each row in the DataFrame
        df['property_name'] = df.apply(find_matching_property, axis=1)

        return df
    except Exception as error:
        logging.error("select_property_and_replace Error: %s", str(error))
        return pd.DataFrame()


def main(mac_data, property_excel_file, output_excel_file):
    ## Read the Excel file into a DataFrame
    compared_df = pd.read_excel(mac_data)

    ## Apply the function to the DataFrame
    updated_df = select_property_and_replace(compared_df, property_excel_file)
    updated_df = updated_df.fillna('')
    # print(f"0.{updated_df.shape}")

    ## Filter the mac_data based on the regex pattern
    property_df = pd.read_excel(property_excel_file)
    filtered_mac_data = tag_units_with_regex_for_all_rows(updated_df, property_df)

    ## Save the filtered DataFrame to a new Excel file
    # print(f"{output_excel_file =}")
    filtered_mac_data.to_excel( output_excel_file, index=False)
    print(filtered_mac_data)



if __name__ ==  "__main__":
    logging.basicConfig(filename ='phy_property_mac_data_validation.log', level=logging.ERROR,\
                         format = '%(asctime)s - %(levelname)s: %(message)s')
    ## Shipment number
    shipment = "240704"

    ## File paths for mac_data and  property_unit data
    mac_data = rf'test_folder\mac_data_{shipment}.xlsx'

    property_excel_file = r"property_unit_data\property_units.xlsx"

    output_excel_file =rf"test_folder\filtered_mac_data_{shipment}.xlsx"
    
    main(mac_data, property_excel_file, output_excel_file)
    
################################################################################
