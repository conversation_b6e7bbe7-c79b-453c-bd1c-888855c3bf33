"""
Test script to verify the updated LLaMA3 module works with new JSON format
"""

import pandas as pd
from get_property_data_from_table_by_llama3_v4 import convert_json_table_to_string_for_llama, extract_data_to_dataframe

# Test data in new JSON format
prop_table_dict_list = [
    {
        'Young modulus': {
            'table_id': 'table_4_2', 
            'title': 'Tensile, impact and brittleness parameters for pristine poly(BA-a) and AC filled composites.', 
            'table_data': [
                {'0': 'Sample', '1': 'Tensile Stress', '2': '(MPa)', '3': 'Young modulus (GPa)', '4': 'Impact strength (kJ/m 2 )', '5': 'Brittleness ( B ) (% Pa/10 10 )', '6': 'Brittleness ( B ) (% Pa/10 10 )'}, 
                {'0': 'P(BA-a)', '1': '27.93 ± 0.78', '2': '27.93 ± 0.78', '3': '2.16 ± 0.122', '4': '1.4 ± 0.12', '5': '3.26', '6': '3.26'}
            ],
            'page_number': 4, 
            'coordinates': {'left': 42.0387077331543, 'top': 707.4191436767578, 'right': 562.8633422851562, 'bottom': 637.2298889160156, 'coord_origin': 'BOTTOMLEFT'}, 
            'page_size': {'width': 595.2760009765625, 'height': 793.7009887695312}
        }
    }
]

def test_json_to_string_conversion():
    """Test the JSON to string conversion function"""
    print("Testing JSON to string conversion...")
    
    for prop_table_dict in prop_table_dict_list:
        for property_name, table_entry in prop_table_dict.items():
            print(f"\nProperty: {property_name}")
            print(f"Original JSON structure keys: {table_entry.keys()}")
            
            # Convert to string
            table_string = convert_json_table_to_string_for_llama(table_entry)
            print(f"Converted string (first 200 chars): {table_string[:200]}...")
            
            return table_string

def test_dataframe_creation():
    """Test DataFrame creation with metadata"""
    print("\nTesting DataFrame creation with metadata...")
    
    # Mock response data (simulating LLaMA3 API response)
    mock_response = {
        'Young modulus': [
            {
                'property_name': 'Young modulus',
                'value': '2.16',
                'unit': 'GPa',
                'compound': 'P(BA-a)'
            },
            {
                'property_name': 'Young modulus', 
                'value': '2.56',
                'unit': 'GPa',
                'compound': 'P(BA-a/AC1)'
            }
        ]
    }
    
    # Extract metadata
    table_metadata = {}
    for prop_table_dict in prop_table_dict_list:
        for property_name, table_entry in prop_table_dict.items():
            if isinstance(table_entry, dict):
                table_metadata[property_name] = table_entry
    
    print(f"Table metadata keys: {table_metadata.keys()}")
    
    # Create DataFrame with metadata
    df = extract_data_to_dataframe(mock_response, table_metadata)
    
    print(f"\nDataFrame shape: {df.shape}")
    print(f"DataFrame columns: {list(df.columns)}")
    print("\nDataFrame content:")
    print(df.to_string())
    
    return df

if __name__ == "__main__":
    print("=" * 60)
    print("TESTING UPDATED LLAMA3 MODULE WITH NEW JSON FORMAT")
    print("=" * 60)
    
    # Test 1: JSON to string conversion
    table_string = test_json_to_string_conversion()
    
    # Test 2: DataFrame creation with metadata
    df = test_dataframe_creation()
    
    print("\n" + "=" * 60)
    print("TESTS COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    
    # Show expected columns
    expected_columns = [
        'property_name', 'value', 'unit', 'compound', 'source_key',
        'table_id', 'page_number', 'coord_left', 'coord_top', 'coord_right', 
        'coord_bottom', 'coord_origin', 'page_width', 'page_height'
    ]
    
    print(f"\nExpected columns: {len(expected_columns)}")
    print(f"Actual columns: {len(df.columns)}")
    print(f"All expected columns present: {all(col in df.columns for col in expected_columns)}")
