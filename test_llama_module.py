"""
Test script to verify the updated LLaMA3 module works with new JSON format
"""

import pandas as pd
from get_property_data_from_table_by_llama3_v4 import (
    extract_data_to_dataframe,
    convert_coordinates_for_fitz,
    convert_table_coordinates_in_list
)

# Test data in new JSON format
prop_table_dict_list = [
    {
        'Young modulus': {
            'table_id': 'table_4_2', 
            'title': 'Tensile, impact and brittleness parameters for pristine poly(BA-a) and AC filled composites.', 
            'table_data': [
                {'0': 'Sample', '1': 'Tensile Stress', '2': '(MPa)', '3': 'Young modulus (GPa)', '4': 'Impact strength (kJ/m 2 )', '5': 'Brittleness ( B ) (% Pa/10 10 )', '6': 'Brittleness ( B ) (% Pa/10 10 )'}, 
                {'0': 'P(BA-a)', '1': '27.93 ± 0.78', '2': '27.93 ± 0.78', '3': '2.16 ± 0.122', '4': '1.4 ± 0.12', '5': '3.26', '6': '3.26'}
            ],
            'page_number': 4, 
            'coordinates': {'left': 42.0387077331543, 'top': 707.4191436767578, 'right': 562.8633422851562, 'bottom': 637.2298889160156, 'coord_origin': 'BOTTOMLEFT'}, 
            'page_size': {'width': 595.2760009765625, 'height': 793.7009887695312}
        }
    }
]

def test_json_structure():
    """Test the JSON structure for LLaMA processing"""
    print("Testing JSON structure for LLaMA processing...")

    for prop_table_dict in prop_table_dict_list:
        for property_name, table_entry in prop_table_dict.items():
            print(f"\nProperty: {property_name}")
            print(f"JSON structure keys: {table_entry.keys()}")

            # Show the structured data that will be sent to LLaMA
            title = table_entry.get('title', '')
            table_data = table_entry.get('table_data', [])

            print(f"Title: {title[:100]}...")
            print(f"Table data rows: {len(table_data)}")
            print(f"First row (headers): {table_data[0] if table_data else 'No data'}")
            print(f"Second row (sample data): {table_data[1] if len(table_data) > 1 else 'No data'}")

            return table_entry

def test_dataframe_creation():
    """Test DataFrame creation with metadata"""
    print("\nTesting DataFrame creation with metadata...")
    
    # Mock response data (simulating LLaMA3 API response)
    mock_response = {
        'Young modulus': [
            {
                'property_name': 'Young modulus',
                'value': '2.16',
                'unit': 'GPa',
                'compound': 'P(BA-a)'
            },
            {
                'property_name': 'Young modulus', 
                'value': '2.56',
                'unit': 'GPa',
                'compound': 'P(BA-a/AC1)'
            }
        ]
    }
    
    # Extract metadata
    table_metadata = {}
    for prop_table_dict in prop_table_dict_list:
        for property_name, table_entry in prop_table_dict.items():
            if isinstance(table_entry, dict):
                table_metadata[property_name] = table_entry
    
    print(f"Table metadata keys: {table_metadata.keys()}")
    
    # Create DataFrame with metadata
    df = extract_data_to_dataframe(mock_response, table_metadata)
    
    print(f"\nDataFrame shape: {df.shape}")
    print(f"DataFrame columns: {list(df.columns)}")
    print("\nDataFrame content:")
    print(df.to_string())
    
    return df

def test_coordinate_conversion():
    """Test coordinate conversion from BOTTOMLEFT to TOPLEFT"""
    print("\nTesting coordinate conversion...")

    # Test data with BOTTOMLEFT coordinates
    original_coordinates = {'left': 42.0387077331543, 'top': 707.4191436767578, 'right': 562.8633422851562, 'bottom': 637.2298889160156, 'coord_origin': 'BOTTOMLEFT'}
    page_size = {'width': 595.2760009765625, 'height': 793.7009887695312}

    print(f"Original coordinates (BOTTOMLEFT): {original_coordinates}")
    print(f"Page size: {page_size}")

    # Convert coordinates
    converted_coords = convert_coordinates_for_fitz(original_coordinates, page_size, 'BOTTOMLEFT')

    print(f"Converted coordinates (TOPLEFT): {converted_coords}")

    # Test with TOPLEFT coordinates (should remain unchanged)
    topleft_coords = {'left': 42.0, 'top': 100.0, 'right': 562.0, 'bottom': 200.0, 'coord_origin': 'TOPLEFT'}
    unchanged_coords = convert_coordinates_for_fitz(topleft_coords, page_size, 'TOPLEFT')

    print(f"\nTOPLEFT coordinates (should remain unchanged):")
    print(f"Original: {topleft_coords}")
    print(f"After conversion: {unchanged_coords}")

    # Test full list conversion
    print(f"\nTesting full list conversion...")
    converted_list = convert_table_coordinates_in_list(prop_table_dict_list)

    for prop_dict in converted_list:
        for prop_name, table_entry in prop_dict.items():
            if isinstance(table_entry, dict):
                coords = table_entry.get('coordinates', {})
                print(f"Property '{prop_name}' coordinates after conversion: {coords}")

    return converted_coords

if __name__ == "__main__":
    print("=" * 60)
    print("TESTING UPDATED LLAMA3 MODULE WITH NEW JSON FORMAT")
    print("=" * 60)
    
    # Test 1: JSON structure for LLaMA
    table_entry = test_json_structure()
    
    # Test 2: DataFrame creation with metadata
    df = test_dataframe_creation()

    # Test 3: Coordinate conversion
    converted_coords = test_coordinate_conversion()

    print("\n" + "=" * 60)
    print("TESTS COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    
    # Show expected columns
    expected_columns = [
        'property_name', 'value', 'unit', 'compound', 'source_key',
        'table_id', 'page_number', 'coord_left', 'coord_top', 'coord_right', 
        'coord_bottom', 'coord_origin', 'page_width', 'page_height'
    ]
    
    print(f"\nExpected columns: {len(expected_columns)}")
    print(f"Actual columns: {len(df.columns)}")
    print(f"All expected columns present: {all(col in df.columns for col in expected_columns)}")
