
"""
This module provides functions to extract property data from tables\
      using the Llama3 model.

The main function `get_prop_data_by_llama3_model` takes a TAN number\
     and a list of dictionaries containing JSON table data as input.\
     It sends a request to the Llama3 model API with the table data,\
     extracts the property data from the response, and returns\
     a pandas DataFrame containing the property data with metadata.

The module handles the new JSON input format with complete table structures
including coordinates, page_number, and page_size metadata.

The module also includes helper functions for sending requests to\
    the API and extracting data from the response.

Date 22-08-24
Updated: [Current Date]
@author: <PERSON>ha<PERSON>
"""


# Imports required
import requests
import json
import pandas as pd



def convert_json_table_to_string_for_llama(table_entry):
    """
    Convert JSON table entry to string format for LLaMA3 model processing.

    Args:
        table_entry (dict): JSON table entry with title, table_data, and metadata

    Returns:
        str: String representation of the table data for LLaMA3 processing
    """
    try:
        if isinstance(table_entry, str):
            # Already a string, return as is
            return table_entry

        if isinstance(table_entry, dict):
            # JSON table entry
            title = table_entry.get('title', '')
            table_data_rows = table_entry.get('table_data', [])

            # Create string representation similar to original format
            string_parts = [title]

            for row in table_data_rows:
                row_values = []
                for key in sorted(row.keys(), key=lambda x: int(x) if x.isdigit() else float('inf')):
                    row_values.append(str(row[key]))
                string_parts.append('\n'.join(row_values))

            return '\n'.join(string_parts)

        return str(table_entry)

    except Exception as error:
        print(f"convert_json_table_to_string_for_llama Error: {str(error)}")
        return str(table_entry)



def get_response(url, prop_table_dict):
    """
    Sends a POST request to the specified URL and retrieves\
         property data from the response.

    Args:
        url (str): The URL to send the POST request to.
        prop_table_dict (dict): A dictionary mapping property\
             names to JSON table entries.

    Returns:
        dict: A dictionary containing the extracted property\
             data in JSON format.
    """
    headers = {'Content-Type': 'application/json'}
    responses = {}


    for property_name, table_entry in prop_table_dict.items():
        # Convert JSON table entry to string format for LLaMA3 processing
        table_data_string = convert_json_table_to_string_for_llama(table_entry)
        prompt = (
            "You are an Expert DATA CURATOR with domain knowledge of Physics and Chemistry. Also knows python programming language to convert text data to tabular format.\n"

            "For Example:- {'meff': 'Table 1\nElemental analysis, molar conductance (Lm) and meff of the ligands and their Zn complexes.\nLigand/Complex\nM:L\nM. Wt.\nEmpirical formulae\nC% (Calcd)\nfound\nH% (Calcd)\nfound\nN% (Calcd)\nfound\nCl% (calcd)\nfound\nM% (Calcd)\nfound\n(Lm)\nmeff\nL1\ne\n354\nC22H18N4O\n(74.57)\n74.47\n(5.08)\n5.17\n(15.82)\n15.85\ne\ne\ne\ne\ne\ne\ne\ne\nL2\ne'}.\n"
            "Here we should get 'values' for property 'meff' as '0.09', '0.07','0.05','0.09','0.05','0.07','0.75','0.59' and unit as 'B.M.'. If unit is not given for the property then use your domain knowledge to provide unit for the same property. For a table unit of the property will be same. Value of a property must be a number. \n\n"

            f"Your task is to Extract the physical property data related to '{property_name}' from all relevant columns of the provided table content. "
            "Ensure to extract all instances of the property from multiple columns. "
            "Return the data in JSON format as a list of dictionaries with keys as 'property_name', 'value', 'unit', and 'compound'. "
            "If the numerical value for a given property is a range of two numbers only, keep this range as it is in  the 'value' key, do not break into separate numbers. Make sure you are not adding non-numerical data like unit into value. For Example:- we get value as '460 J/kg°C', then take only number '460' as 'value' key ignoring the text after it. If you get any repeated data for a property, then do not drop the repeated data. \n"

            f"Always use '{property_name}' as 'property_name' key and ensure consistency in 'unit' key for same property, as each property will have same unique unit in given table. "
            "If a property is unitless, use an empty string for 'unit' key. Do the same if you do not find any unit for the property. "
            "For a property , unit will be same for the entire table. "
            "Use context to determine the 'compound' if not explicitly stated. "
            "Ensure that no data is missed for the given property and extract data from all relevant columns. "
            "Columns may contain the same property data, ensure all instances are extracted."
            "Extract the data in string format. Only return the JSON list without extra explanation."
            "Do not change data like 101.0 to 101, keep the data in same format as input provided.\n\n"

            f"Table content:\n{table_data_string}\n"
        )
        # "Avoid duplicates and use domain knowledge to determine units if not explicitly provided. "
        payload = json.dumps({"prompt": prompt})

        # Send a GET request to the specified URL
        response = requests.post(url, headers=headers, data=payload)

        # Store the response
        try:
            responses[property_name] = json.loads(response.text)
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON for property {property_name}: {e}")
            responses[property_name] = []

    return responses



def extract_data_to_dataframe(response, table_metadata=None):
    """
    Extract data from the response and convert it into a DataFrame with all data in string format.
    Includes metadata columns for coordinates, page_number, and page_size.

    Args:
        response (dict): The response object containing data.
        table_metadata (dict): Dictionary containing table metadata for each property.
    Returns:
        pd.DataFrame: A DataFrame with all data in string format including metadata.
    """
    # Check if the response contains data
    if not response:
        return pd.DataFrame()

    # Initialize a list to hold DataFrames
    df_list = []

    # Iterate over each key-value pair in the response
    for key, data in response.items():
        # Convert the data to a DataFrame
        df = pd.DataFrame(data)

        # Ensure all columns are in string format
        for col in df.columns:
            df[col] = df[col].astype(str)

        # Add a column to indicate the data source (key)
        df['source_key'] = key

        # Add metadata columns if available
        if table_metadata and key in table_metadata:
            metadata = table_metadata[key]

            # Add table_id
            df['table_id'] = str(metadata.get('table_id', ''))

            # Add page_number
            df['page_number'] = str(metadata.get('page_number', ''))

            # Add coordinates as separate columns
            coordinates = metadata.get('coordinates', {})
            df['coord_left'] = str(coordinates.get('left', ''))
            df['coord_top'] = str(coordinates.get('top', ''))
            df['coord_right'] = str(coordinates.get('right', ''))
            df['coord_bottom'] = str(coordinates.get('bottom', ''))
            df['coord_origin'] = str(coordinates.get('coord_origin', ''))

            # Add page_size as separate columns
            page_size = metadata.get('page_size', {})
            df['page_width'] = str(page_size.get('width', ''))
            df['page_height'] = str(page_size.get('height', ''))
        else:
            # Add empty metadata columns if no metadata available
            df['table_id'] = ''
            df['page_number'] = ''
            df['coord_left'] = ''
            df['coord_top'] = ''
            df['coord_right'] = ''
            df['coord_bottom'] = ''
            df['coord_origin'] = ''
            df['page_width'] = ''
            df['page_height'] = ''

        # Append the DataFrame to the list
        df_list.append(df)

    # Concatenate all DataFrames
    result_df = pd.concat(df_list, ignore_index=True)

    return result_df

def get_prop_data_by_llama3_model(tan_number, prop_table_dict_list, output_file):
    """
    Extract property data from a list of property table dictionaries using the LLaMA3 model.
    Args:
        tan_number (str): The TAN number for the compound.
        prop_table_dict_list (list): A list of dictionaries containing JSON property table data.
        output_file (str): The file path to save the output Excel file.
    Returns:
        pd.DataFrame: A DataFrame containing the extracted property data with metadata.
    """
    # Initialize an empty list to store the dataframes
    tan_df_list = []

    # Define the URL
    url = "http://172.27.7.85:5007/lamma_3_1_model"
    # url= "http://172.27.7.85:5007/lamma_3_2_model"
    # url = "http://172.27.7.85:5007/mistral_model"

    # Iterate over each property table dictionary
    for prop_table_dict in prop_table_dict_list:
        # Extract metadata for each property
        table_metadata = {}
        for property_name, table_entry in prop_table_dict.items():
            if isinstance(table_entry, dict):
                table_metadata[property_name] = table_entry

        # Get the response from the LLaMA3 model
        response = get_response(url, prop_table_dict)

        # Extract the data from the response and ensure all data is in string format
        # Pass metadata to include coordinates, page_number, and page_size
        df = extract_data_to_dataframe(response, table_metadata)

        # Check if the data is not empty
        if not df.empty:
            tan_df_list.append(df)

    # Concatenate the dataframes
    if tan_df_list:
        tan_df = pd.concat(tan_df_list, ignore_index=True)
        tan_df['tan_name'] = tan_number
    else:
        # Return an empty dataframe if no data was extracted with all expected columns
        tan_df = pd.DataFrame(columns=[
            'property_name', 'value', 'unit', 'compound', 'source_key', 'tan_name',
            'table_id', 'page_number', 'coord_left', 'coord_top', 'coord_right',
            'coord_bottom', 'coord_origin', 'page_width', 'page_height'
        ])

    # Convert all columns to string explicitly (if needed)
    tan_df = tan_df.astype(str)

    # Save to Excel while preserving data types as strings
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        tan_df.to_excel(writer, index=False)
    # print(tan_df)
    # Return the dataframe
    return tan_df




if __name__ == "__main__":
    # Get the TAN number of pdf file
    tan_number = "41484277K"

    ### Get the property table data dictionary list
    # prop_table_dict_list  = [
    #     {'Cp': 'Table 1. Material properties at room temperature for heat transfer model.\nMaterial properties\nDensity\nSpecific heat\nThermal conductivity\nReference\n\x02 (kg/m3)\nCp (J/kg·°C)\nkxx, kyy\n(W/m\x04\x02C)\nkzz\n(W/m\x04\x02C)\nMetal mesh\n7780\n460\n10\n10\nRef. 28\nWood insulator\n1242\n1090\n0.12\n0.12\nRef. 28\nPEI resin film\n1270\n980\n0.22\n0.22\n\x02, k: Ref. 26; Cp: Ref. 29\nGF/PEI\n1930\n890\n0.53\n0.4\nMeasured\nGF/PEI:glass fibre/polyetherimide.'},

    #     {'Tg': 'Table S2. Thermal material properties of the BPURs. \nComposition \nTg, DMTA (°C) \nTg, DSC (°C) \nBPUR 10HS \n-31.7 ± 0.2*\n,X,^ \n-43.5 ± 0.7*\n, ǂ \nBPUR 30HS \n-28.9 ± 1.2*\n, \n-41.5 ± 0.5^ \nBPUR 50HS \n-27.2 ± 1.2 \nX, ǂ \n-41.1 ± 0.2\nǂ, X \nPCL-PUR 30HS \n-44.4 ± 2.1^\n, , ǂ \n-59.0 ± 1.0*\n,^\n,X'},
    # ]
    prop_table_dict_list  = [
    {
    'Young modulus': 
    {
    'table_id': 'table_4_2', 
    'title': 'Tensile, impact and brittleness parameters for pristine poly(BA-a) and AC filled composites.',
    'table_data': [
    {'0': 'Sample', '1': 'Tensile Stress', '2': '(MPa)', '3': 'Young modulus (GPa)', '4': 'Impact strength (kJ/m 2 )', '5': 'Brittleness ( B ) (% Pa/10 10 )', '6': 'Brittleness ( B ) (% Pa/10 10 )'},
     {'0': 'P(BA-a)', '1': '27.93 ± 0.78', '2': '27.93 ± 0.78', '3': '2.16 ± 0.122', '4': '1.4 ± 0.12', '5': '3.26', '6': '3.26'}, 
     {'0': 'P(BA-a/AC1)', '1': '32.49 ± 1.21', '2': '32.49 ± 1.21', '3': '2.56 ± 0.191', '4': '2.7 ± 0.12', '5': '2.74', '6': '2.74'}, 
     {'0': 'P(BA-a/AC2)', '1': '39.55 ± 1.53', '2': '39.55 ± 1.53', '3': '2.84 ± 0.182', '4': '3.9 ± 0.14', '5': '2.50', '6': '2.50'}, 
     {'0': 'P(BA-a/AC3)', '1': '43.03 ± 1.12', '2': '43.03 ± 1.12', '3': '3.10 ± 0.175', '4': '4.7 ± 0.22', '5': '2.35', '6': '2.35'}, 
     {'0': 'P(BA-a/AC4)', '1': '44.84 ± 1.31', '2': '44.84 ± 1.31', '3': '3.31 ± 0.205', '4': '5.7 ± 0.21', '5': '2.15', '6': '2.15'}, 
     {'0': 'P(BA-a/AC5)', '1': '48.82 ± 1.19', '2': '48.82 ± 1.19', '3': '3.54 ± 0.174', '4': '5.2 ± 0.16', '5': '2.12', '6': '2.12'}
     ],
    'page_number': 4,
     'coordinates': {'left': 42.0387077331543, '2.8633top': 707.4191436767578, 'right': 562.8633422851562, 'bottom': 637.2298889160156, 'coord_origin': 'BOTTOMLEFT'},
     'page_size': {'width': 595.2760009765625, 'height': 793.7009887695312}
     }}
    ]
        
    

    output_file =f"{tan_number}_prop_data.xlsx"

    # Get the property data from the LLaMA3 model using the\
    #  TAN number and property table dictionary list
    prop_df = get_prop_data_by_llama3_model(tan_number, prop_table_dict_list, output_file)
    # Save the property data to an Excel file
    prop_df.to_excel(output_file, index=False)


    print(prop_df.to_markdown())
    
####################################################################


###page_number	coord_left	coord_top	coord_right	coord_bottom	coord_origin	page_width	page_height
