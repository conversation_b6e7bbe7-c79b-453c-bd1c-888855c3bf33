"""
This module provides functions to extract property data from tables\
    using the Llama3 model.

The main function `get_prop_data_by_llama3_model` takes a TAN number\
    and a list of dictionaries containing JSON table data as input.\
    It sends a request to the Llama3 model API with the table data,\
    extracts the property data from the response, and returns\
    a pandas DataFrame containing the property data with metadata.

The module handles the new JSON input format with complete table structures
including coordinates, page_number, and page_size metadata.

The module also includes helper functions for sending requests to\
    the API and extracting data from the response.

Date: 10-09-2025
@author: <PERSON>ha<PERSON>
"""


# Imports required
import requests
import json
import pandas as pd
import fitz  # PyMuPDF for coordinate conversion



def convert_coordinates_for_fitz(coordinates, page_size, coord_origin):
    """
    Convert coordinates from BOTTOMLEFT origin to TOPLEFT origin for FITZ module compatibility.

    Args:
        coordinates (dict): Dictionary containing coordinate values (left, top, right, bottom)
        page_size (dict): Dictionary containing page dimensions (width, height)
        coord_origin (str): Origin of the coordinate system ('BOTTOMLEFT' or 'TOPLEFT')

    Returns:
        dict: Converted coordinates for FITZ module (TOPLEFT origin)
    """
    try:
        # If already TOPLEFT, return coordinates unchanged
        if coord_origin.upper() == 'TOPLEFT':
            return {
                'left': coordinates.get('left', 0),
                'top': coordinates.get('top', 0),
                'right': coordinates.get('right', 0),
                'bottom': coordinates.get('bottom', 0),
                'coord_origin': 'TOPLEFT'
            }

        # Convert from BOTTOMLEFT to TOPLEFT
        elif coord_origin.upper() == 'BOTTOMLEFT':
            page_height = page_size.get('height', 0)

            # In BOTTOMLEFT: (0,0) is at bottom-left corner
            # In TOPLEFT: (0,0) is at top-left corner
            # Conversion: new_y = page_height - old_y

            converted_coordinates = {
                'left': coordinates.get('left', 0),
                'top': page_height - coordinates.get('bottom', 0),  # bottom becomes top
                'right': coordinates.get('right', 0),
                'bottom': page_height - coordinates.get('top', 0),  # top becomes bottom
                'coord_origin': 'TOPLEFT'
            }

            return converted_coordinates

        else:
            # Unknown coordinate origin, return original coordinates
            print(f"Warning: Unknown coordinate origin '{coord_origin}'. Returning original coordinates.")
            return {
                'left': coordinates.get('left', 0),
                'top': coordinates.get('top', 0),
                'right': coordinates.get('right', 0),
                'bottom': coordinates.get('bottom', 0),
                'coord_origin': coord_origin
            }

    except Exception as error:
        print(f"Error in convert_coordinates_for_fitz: {str(error)}")
        return {
            'left': coordinates.get('left', 0),
            'top': coordinates.get('top', 0),
            'right': coordinates.get('right', 0),
            'bottom': coordinates.get('bottom', 0),
            'coord_origin': coord_origin
        }



def convert_table_coordinates_in_list(prop_table_dict_list):
    """
    Convert coordinates for all tables in the input list to FITZ-compatible format.

    Args:
        prop_table_dict_list (list): List of property table dictionaries with JSON table data

    Returns:
        list: Updated list with converted coordinates
    """
    try:
        converted_list = []

        for prop_table_dict in prop_table_dict_list:
            converted_dict = {}

            for property_name, table_entry in prop_table_dict.items():
                if isinstance(table_entry, dict):
                    # Create a copy of the table entry
                    converted_entry = table_entry.copy()

                    # Get coordinates, page_size, and coord_origin
                    coordinates = table_entry.get('coordinates', {})
                    page_size = table_entry.get('page_size', {})
                    coord_origin = coordinates.get('coord_origin', 'TOPLEFT')

                    # Convert coordinates if needed
                    converted_coords = convert_coordinates_for_fitz(coordinates, page_size, coord_origin)

                    # Update the coordinates in the entry
                    converted_entry['coordinates'] = converted_coords

                    converted_dict[property_name] = converted_entry
                else:
                    # If not a dict (e.g., string format), keep as is
                    converted_dict[property_name] = table_entry

            converted_list.append(converted_dict)

        return converted_list

    except Exception as error:
        print(f"Error in convert_table_coordinates_in_list: {str(error)}")
        return prop_table_dict_list



# Note: Removed convert_json_table_to_string_for_llama function as we now use JSON data directly
# This is more efficient and provides better accuracy for the LLaMA model



def get_response_0(url, prop_table_dict):
    """
    Sends a POST request to the specified URL and retrieves\
        property data from the response.

    Args:
        url (str): The URL to send the POST request to.
        prop_table_dict (dict): A dictionary mapping property\
            names to JSON table entries.

    Returns:
        dict: A dictionary containing the extracted property\
            data in JSON format.
    """
    headers = {'Content-Type': 'application/json'}
    responses = {}


    for property_name, table_entry in prop_table_dict.items():
        # Use JSON data directly - more efficient and accurate
        table_title = table_entry.get('title', '') if isinstance(table_entry, dict) else ''
        table_data_json = table_entry.get('table_data', []) if isinstance(table_entry, dict) else table_entry

        prompt = (
            "You are an Expert DATA CURATOR with domain knowledge of Physics and Chemistry. You excel at extracting structured data from JSON table format.\n\n"

            "EXAMPLE INPUT FORMAT:\n"
            "{\n"
            "  'title': 'Table 1. Elemental analysis and meff of ligands and Zn complexes',\n"
            "  'table_data': [\n"
            "    {'0': 'Ligand/Complex', '1': 'M:L', '2': 'meff (B.M.)', '3': 'Conductance'},\n"
            "    {'0': 'L1', '1': '1:2', '2': '0.09', '3': '15.2'},\n"
            "    {'0': 'L2', '1': '1:2', '2': '0.07', '3': '18.5'}\n"
            "  ]\n"
            "}\n\n"

            "EXPECTED OUTPUT for property 'meff':\n"
            "[\n"
            "  {'property_name': 'meff', 'value': '0.09', 'unit': 'B.M.', 'compound': 'L1'},\n"
            "  {'property_name': 'meff', 'value': '0.07', 'unit': 'B.M.', 'compound': 'L2'}\n"
            "]\n\n"

            f"TASK: Extract all data for property '{property_name}' from the structured table below.\n\n"

            "INSTRUCTIONS:\n"
            f"1. Find all columns containing '{property_name}' data (check column headers and values)\n"
            "2. Extract ALL instances of the property from multiple columns if present\n"
            "3. For each data point, determine the corresponding compound/sample from the first column\n"
            "4. CRITICAL: Extract units from column headers (e.g., '(MPa)', '(GPa)')"
            "5. If no unit in header, use domain knowledge for common units\n"
            "6. Keep numerical ranges as-is (e.g., '2.5-3.0' stays '2.5-3.0')\n"
            "7. Extract ONLY numerical values for 'value' field (no units in value)\n"
            "8. Put extracted unit in separate 'unit' field\n"
            "9. If unit unclear, examine full table context before defaulting to empty string\n"
            "10. Maintain data format exactly as provided (don't change '101.0' to '101')\n"
            "11. Include repeated data if present\n\n"

            f"REQUIREMENTS:\n"
            f"- property_name: Always use '{property_name}'\n"
            "- value: Only numerical data (no units)\n"
            "- unit: Consistent unit for all entries (empty string if unitless)\n"
            "- compound: Sample/compound name from context\n\n"

            "Return ONLY the JSON array without explanation.\n\n"

            f"TABLE TITLE: {table_title}\n\n"
            f"TABLE DATA (JSON):\n{json.dumps(table_data_json, indent=2)}\n"
        )
        # "Avoid duplicates and use domain knowledge to determine units if not explicitly provided. "
        payload = json.dumps({"prompt": prompt})

        # Send a GET request to the specified URL
        response = requests.post(url, headers=headers, data=payload)

        # Store the response
        try:
            responses[property_name] = json.loads(response.text)
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON for property {property_name}: {e}")
            responses[property_name] = []

    return responses

def get_response_1(url, prop_table_dict):
    """
    Sends a POST request with enhanced example-based prompt.
    """
    headers = {'Content-Type': 'application/json'}
    responses = {}

    # Determine model type from URL for specific handling
    model_type = "unknown"
    if "lamma_3_1_model" in url:
        model_type = "llama3.1"
    elif "lamma_3_2_model" in url:
        model_type = "llama3.2"
    elif "mistral_model" in url:
        model_type = "mistral"

    for property_name, table_entry in prop_table_dict.items():
        table_title = table_entry.get('title', '') if isinstance(table_entry, dict) else ''
        table_data_json = table_entry.get('table_data', []) if isinstance(table_entry, dict) else table_entry

        # Enhanced prompt with complete example
        prompt = (
            "You are an Expert DATA CURATOR with domain knowledge of Physics and Chemistry. You excel at extracting structured data from JSON table format.\n\n"

            "COMPLETE EXAMPLE:\n\n"

            "EXAMPLE INPUT:\n"
            "Property to extract: 'tensile strength'\n"
            "Table Title: 'Table 1. Mechanical properties of polymer composites'\n"
            "Table Data:\n"
            "[\n"
            "  {'0': 'Sample', '1': 'Tensile Strength (MPa)', '2': 'Young Modulus (GPa)', '3': 'Elongation (%)'},\n"
            "  {'0': 'Pure Polymer', '1': '45.2 ± 1.1', '2': '2.8 ± 0.15', '3': '12.5 ± 0.8'},\n"
            "  {'0': 'Composite-1', '1': '52.7 ± 1.3', '2': '3.2 ± 0.18', '3': '10.8 ± 0.6'},\n"
            "  {'0': 'Composite-2', '1': '48.9 ± 0.9', '2': '3.0 ± 0.12', '3': '11.2 ± 0.7'}\n"
            "]\n\n"

            "EXAMPLE OUTPUT for 'tensile strength':\n"
            "[\n"
            "  {\"property_name\": \"tensile strength\", \"value\": \"45.2 ± 1.1\", \"unit\": \"MPa\", \"compound\": \"Pure Polymer\"},\n"
            "  {\"property_name\": \"tensile strength\", \"value\": \"52.7 ± 1.3\", \"unit\": \"MPa\", \"compound\": \"Composite-1\"},\n"
            "  {\"property_name\": \"tensile strength\", \"value\": \"48.9 ± 0.9\", \"unit\": \"MPa\", \"compound\": \"Composite-2\"}\n"
            "]\n\n"

            "EXPLANATION OF EXAMPLE:\n"
            "1. Found column '1' with header 'Tensile Strength (MPa)' - matches requested property\n"
            "2. Extracted unit 'MPa' from column header\n"
            "3. Extracted values from column '1': '45.2 ± 1.1', '52.7 ± 1.3', '48.9 ± 0.9'\n"
            "4. Matched compounds from column '0': 'Pure Polymer', 'Composite-1', 'Composite-2'\n"
            "5. Used requested property name 'tensile strength' for all entries\n\n"

            "CRITICAL JSON FORMAT REQUIREMENTS:\n"
            "- Use ONLY double quotes (\") for all strings\n"
            "- NO single quotes (') anywhere in the response\n"
            "- Return ONLY valid JSON array format\n"
            "- NO markdown code blocks or explanations\n"
            "- NO trailing commas\n"
            "- Keep numerical values exactly as they appear (with ± symbols, ranges, etc.)\n\n"

            f"YOUR TASK: Extract ONLY data for property '{property_name}' from the table below.\n\n"

            "STEP-BY-STEP PROCESS:\n"
            f"1. Find the column that contains '{property_name}' data (check headers carefully)\n"
            f"2. Extract ONLY values from that specific column (ignore other columns)\n"
            "3. Get compound/sample names from the first column (column '0')\n"
            "4. Extract unit from the matched column header or use domain knowledge\n"
            f"5. Use '{property_name}' as property_name for all entries\n"
            "6. Return valid JSON array with double quotes only\n\n"

            f"INPUT DATA:\n"
            f"Property to extract: '{property_name}'\n"
            f"Table Title: {table_title}\n"
            f"Table Data:\n{json.dumps(table_data_json, indent=2)}\n\n"

            "RESPOND WITH VALID JSON ARRAY ONLY (no explanations, no markdown):"
        )

        payload = json.dumps({"prompt": prompt})
        response = requests.post(url, headers=headers, data=payload)

        # Model-specific response parsing
        try:
            response_text = response.text.strip()
            print(f"Raw response for {property_name} ({model_type}): {response_text[:200]}...")

            parsed_response = parse_model_response(response_text, model_type, property_name)
            responses[property_name] = parsed_response

        except Exception as e:
            print(f"Error processing response for property {property_name}: {e}")
            responses[property_name] = []

    return responses

def get_response(url, prop_table_dict):
    """
    Sends a POST request to the specified URL and retrieves property data from the response.
    Enhanced with better JSON parsing.
    """
    headers = {'Content-Type': 'application/json'}
    responses = {}

    for property_name, table_entry in prop_table_dict.items():
        # Use JSON data directly - more efficient and accurate
        table_title = table_entry.get('title', '') if isinstance(table_entry, dict) else ''
        table_data_json = table_entry.get('table_data', []) if isinstance(table_entry, dict) else table_entry

        prompt = (
            "You are an Expert DATA CURATOR with domain knowledge of Physics and Chemistry. You excel at extracting structured data from JSON table format.\n\n"

            "EXAMPLE INPUT FORMAT:\n"
            "{\n"
            "  'title': 'Table 1. Elemental analysis and meff of ligands and Zn complexes',\n"
            "  'table_data': [\n"
            "    {'0': 'Ligand/Complex', '1': 'M:L', '2': 'meff (B.M.)', '3': 'Conductance'},\n"
            "    {'0': 'L1', '1': '1:2', '2': '0.09', '3': '15.2'},\n"
            "    {'0': 'L2', '1': '1:2', '2': '0.07', '3': '18.5'}\n"
            "  ]\n"
            "}\n\n"

            "EXPECTED OUTPUT for property 'meff':\n"
            "[\n"
            "  {\"property_name\": \"meff\", \"value\": \"0.09\", \"unit\": \"B.M.\", \"compound\": \"L1\"},\n"
            "  {\"property_name\": \"meff\", \"value\": \"0.07\", \"unit\": \"B.M.\", \"compound\": \"L2\"}\n"
            "]\n\n"

            f"TASK: Extract all data for property '{property_name}' from the structured table below.\n\n"

            "INSTRUCTIONS:\n"
            f"1. Find all columns containing '{property_name}' data (check column headers and values)\n"
            "2. Extract ALL instances of the property from multiple columns if present\n"
            "3. For each data point, determine the corresponding compound/sample from the first column\n"
            "4. Identify the unit from column headers or use domain knowledge\n"
            "5. Keep numerical ranges as-is (e.g., '2.5-3.0' stays '2.5-3.0')\n"
            "6. Extract only numerical values (remove units from value field)\n"
            "7. Maintain data format exactly as provided (don't change '101.0' to '101')\n"
            "8. Include repeated data if present\n\n"

            f"REQUIREMENTS:\n"
            f"- property_name: Always use '{property_name}'\n"
            "- value: Only numerical data (no units)\n"
            "- unit: Consistent unit for all entries (empty string if unitless)\n"
            "- compound: Sample/compound name from context\n\n"

            "CRITICAL: Return ONLY valid JSON array with proper commas between objects.\n"
            "Format: [{\"property_name\": \"X\", \"value\": \"Y\", \"unit\": \"Z\", \"compound\": \"W\"}]\n\n"

            f"TABLE TITLE: {table_title}\n\n"
            f"TABLE DATA (JSON):\n{json.dumps(table_data_json, indent=2)}\n"
        )

        payload = json.dumps({"prompt": prompt})

        # Send a POST request to the specified URL
        response = requests.post(url, headers=headers, data=payload)

        # Enhanced JSON parsing with error handling
        try:
            response_text = response.text.strip()
            parsed_response = safe_json_parse(response_text, property_name)
            responses[property_name] = parsed_response
            
        except Exception as e:
            print(f"Error processing response for property {property_name}: {e}")
            responses[property_name] = []

    return responses



def parse_model_response(response_text, model_type, property_name):
            """
            Parse model response with model-specific handling.

            Args:
                response_text (str): Raw response from model
                model_type (str): Type of model (llama3.1, llama3.2, mistral)
                property_name (str): Property being extracted
    
            Returns:
                list: Parsed JSON data
            """
            try:
                # Strategy 1: Direct JSON parsing
                return json.loads(response_text)
    
            except json.JSONDecodeError as e:
                print(f"Direct JSON parsing failed for {model_type}: {e}")
    
                try:
                    # Strategy 2: Clean and fix common issues
                    cleaned_response = clean_model_response(response_text, model_type)
                    return json.loads(cleaned_response)
        
                except json.JSONDecodeError as e:
                    print(f"Cleaned JSON parsing failed for {model_type}: {e}")
        
                    try:
                        # Strategy 3: Aggressive quote fixing
                        fixed_response = fix_quotes_aggressively(response_text)
                        return json.loads(fixed_response)
            
                    except json.JSONDecodeError as e:
                        print(f"Quote fixing failed for {model_type}: {e}")
            
                        try:
                            # Strategy 4: Manual extraction
                            return extract_data_manually(response_text, property_name)
                
                        except Exception as e:
                            print(f"Manual extraction failed for {model_type}: {e}")
                            return []


def clean_model_response(response_text, model_type):
            """
            Clean model response based on model type.

            Args:
                response_text (str): Raw response text
                model_type (str): Model type
    
            Returns:
                str: Cleaned response text
            """
            # Remove markdown code blocks
            response_text = response_text.strip()

            # Remove common markdown patterns
            patterns_to_remove = [
                r'\s*',
                r'\s*',
                r'^json\s*',
                r'Here.*?:\s*',
                r'The.*?is:\s*',
                r'Response:\s*',
                r'Output:\s*'
            ]

            for pattern in patterns_to_remove:
                response_text = re.sub(pattern, '', response_text, flags=re.IGNORECASE | re.MULTILINE)

            response_text = response_text.strip()

            # Model-specific cleaning
            if model_type == "llama3.1":
                # LLaMA 3.1 specific issues
                response_text = fix_llama31_issues(response_text)
            elif model_type == "mistral":
                # Mistral specific issues
                response_text = fix_mistral_issues(response_text)

            return response_text


def fix_llama31_issues(response_text):
            """
            Fix common LLaMA 3.1 response issues.
            """
            # LLaMA 3.1 often uses single quotes or has formatting issues
            # Replace single quotes with double quotes, but be careful with contractions

            # First, protect contractions
            contractions = re.findall(r"\b\w+'\w+\b", response_text)
            temp_response = response_text

            for i, contraction in enumerate(contractions):
                placeholder = f"__CONTRACTION_{i}__"
                temp_response = temp_response.replace(contraction, placeholder, 1)

            # Fix property names and values with single quotes
            temp_response = re.sub(r"'([^']*?)'(\s*:)", r'"\1"\2', temp_response)  # Keys
            temp_response = re.sub(r":\s*'([^']*?)'", r': "\1"', temp_response)    # Values
            temp_response = re.sub(r"\[\s*'([^']*?)'", r'["\1"', temp_response)    # Array start
            temp_response = re.sub(r",\s*'([^']*?)'", r', "\1"', temp_response)    # Array items

            # Restore contractions
            for i, contraction in enumerate(contractions):
                placeholder = f"__CONTRACTION_{i}__"
                temp_response = temp_response.replace(placeholder, contraction)

            return temp_response


def fix_mistral_issues(response_text):
            """
            Fix common Mistral response issues.
            """
            # Mistral often has similar issues to LLaMA 3.1
            return fix_llama31_issues(response_text)


def fix_quotes_aggressively(response_text):
            """
            Aggressively fix quote issues as last resort.
            """
            # This is a more aggressive approach - use with caution
            import re

            # Replace all single quotes with double quotes
            # This might break some strings, but it's a last resort
            response_text = response_text.replace("'", '"')

            # Fix common issues that might arise from aggressive replacement
            response_text = re.sub(r'"\s*(\w+)\s*"(\s*:)', r'"\1"\2', response_text)  # Fix spaced keys

            return response_text


def extract_data_manually(response_text, property_name):
            """
            Manual extraction when JSON parsing completely fails.
            """
            try:
                # Look for patterns like property_name: value, unit: something, compound: something
                import re
    
                # This is a basic pattern matcher - you might need to enhance it
                pattern = r'property_name["\s:]*([^,}]+)[,}].*?value["\s:]*([^,}]+)[,}].*?unit["\s:]*([^,}]+)[,}].*?compound["\s:]*([^,}]+)[,}]'
    
                matches = re.findall(pattern, response_text, re.IGNORECASE | re.DOTALL)
    
                result = []
                for match in matches:
                    result.append({
                        "property_name": match[0].strip().strip('"\''),
                        "value": match[1].strip().strip('"\''),
                        "unit": match[2].strip().strip('"\''),
                        "compound": match[3].strip().strip('"\'')
                    })
    
                return result
    
            except Exception as e:
                print(f"Manual extraction failed: {e}")
                return []

def validate_extracted_data(response, prop_table_dict):
    """
    Validate that extracted values come from correct columns
    """
    validated_response = {}

    for property_name, extracted_data in response.items():
        if property_name not in prop_table_dict:
            continue
        
        table_entry = prop_table_dict[property_name]
        table_data = table_entry.get('table_data', []) if isinstance(table_entry, dict) else []
    
        if not table_data:
            validated_response[property_name] = extracted_data
            continue
        
        # Find the correct column for this property
        header_row = table_data[0] if table_data else {}
        matching_columns = []
    
        for col_key, col_header in header_row.items():
            if property_name.lower() in col_header.lower():
                matching_columns.append(col_key)
    
        # Validate extracted values against matching columns
        valid_data = []
        for item in extracted_data:
            value = item.get('value', '')
            # Check if this value exists in any of the matching columns
            value_found = False
            for row in table_data[1:]:  # Skip header row
                for col_key in matching_columns:
                    if col_key in row and str(value) in str(row[col_key]):
                        value_found = True
                        break
                if value_found:
                    break
        
            if value_found:
                valid_data.append(item)
    
        validated_response[property_name] = valid_data

    return validated_response


def extract_data_to_dataframe(response, table_metadata=None):
    """
    Extract data from the response and convert it into a DataFrame with all data in string format.
    Includes metadata columns for coordinates, page_number, and page_size.

    Args:
        response (dict): The response object containing data.
        table_metadata (dict): Dictionary containing table metadata for each property.
    Returns:
        pd.DataFrame: A DataFrame with all data in string format including metadata.
    """
    # Check if the response contains data
    if not response:
        return pd.DataFrame()

    # Initialize a list to hold DataFrames
    df_list = []

    # Iterate over each key-value pair in the response
    for key, data in response.items():
        # Convert the data to a DataFrame
        df = pd.DataFrame(data)

        # Ensure all columns are in string format
        for col in df.columns:
            df[col] = df[col].astype(str)

        # Add a column to indicate the data source (key)
        df['source_key'] = key
        df['property_name'] = key

        # Add metadata columns if available
        if table_metadata and key in table_metadata:
            metadata = table_metadata[key]

            # Add table_id
            df['table_id'] = str(metadata.get('table_id', ''))

            # Add page_number
            df['page_number'] = str(metadata.get('page_number', ''))

            # Add coordinates as separate columns
            coordinates = metadata.get('coordinates', {})
            df['coord_left'] = str(coordinates.get('left', ''))
            df['coord_top'] = str(coordinates.get('top', ''))
            df['coord_right'] = str(coordinates.get('right', ''))
            df['coord_bottom'] = str(coordinates.get('bottom', ''))
            df['coord_origin'] = str(coordinates.get('coord_origin', ''))

            # Add page_size as separate columns
            page_size = metadata.get('page_size', {})
            df['page_width'] = str(page_size.get('width', ''))
            df['page_height'] = str(page_size.get('height', ''))
        else:
            # Add empty metadata columns if no metadata available
            df['table_id'] = ''
            df['page_number'] = ''
            df['coord_left'] = ''
            df['coord_top'] = ''
            df['coord_right'] = ''
            df['coord_bottom'] = ''
            df['coord_origin'] = ''
            df['page_width'] = ''
            df['page_height'] = ''

        # Append the DataFrame to the list
        df_list.append(df)

    # Concatenate all DataFrames
    result_df = pd.concat(df_list, ignore_index=True)

    return result_df

import json
import re

def convert_single_quotes_to_double_quotes(prop_table_dict_list):
    """
    Convert all single quotes to double quotes in the prop_table_dict_list to ensure valid JSON format.

    Args:
        prop_table_dict_list (list): List of dictionaries containing property table data with potential single quotes
    
    Returns:
        list: Same list structure but with all single quotes converted to double quotes for valid JSON
    """
    try:
        converted_list = []
    
        for prop_table_dict in prop_table_dict_list:
            converted_dict = {}
        
            for property_name, table_entry in prop_table_dict.items():
                if isinstance(table_entry, dict):
                    # Convert the entire dictionary to JSON string and back to ensure proper quoting
                    json_string = json.dumps(table_entry, ensure_ascii=False)
                    converted_entry = json.loads(json_string)
                    converted_dict[property_name] = converted_entry
                elif isinstance(table_entry, str):
                    # For string entries, convert single quotes to double quotes carefully
                    converted_dict[property_name] = convert_string_quotes(table_entry)
                else:
                    # For other types, keep as is
                    converted_dict[property_name] = table_entry
        
            converted_list.append(converted_dict)
    
        return converted_list
    
    except Exception as error:
        print(f"Error in convert_single_quotes_to_double_quotes: {str(error)}")
        return prop_table_dict_list


def convert_string_quotes(text):
    """
    Helper function to convert single quotes to double quotes in string content.
    Handles edge cases like contractions and nested quotes.

    Args:
        text (str): Input string with potential single quotes
    
    Returns:
        str: String with single quotes converted to double quotes where appropriate
    """
    try:
        # Handle contractions and possessives (don't convert these single quotes)
        # Pattern to match contractions like don't, can't, it's, etc.
        contraction_pattern = r"\b\w+'\w+\b"
        contractions = re.findall(contraction_pattern, text)
    
        # Temporarily replace contractions with placeholders
        temp_text = text
        placeholders = {}
        for i, contraction in enumerate(contractions):
            placeholder = f"__CONTRACTION_{i}__"
            placeholders[placeholder] = contraction
            temp_text = temp_text.replace(contraction, placeholder, 1)
    
        # Convert remaining single quotes to double quotes
        temp_text = temp_text.replace("'", '"')
    
        # Restore contractions
        for placeholder, contraction in placeholders.items():
            temp_text = temp_text.replace(placeholder, contraction)
    
        return temp_text
    
    except Exception as error:
        print(f"Error in convert_string_quotes: {str(error)}")
        return text


def convert_quotes_comprehensive(prop_table_dict_list):
    """
    Comprehensive function to convert single quotes to double quotes throughout the entire data structure.
    This handles nested dictionaries, lists, and string values recursively.

    Args:
        prop_table_dict_list (list): List of dictionaries containing property table data
    
    Returns:
        list: Converted list with proper double quotes for JSON compatibility
    """
    try:
        # Convert the entire structure to JSON string and back
        # This ensures all quotes are properly formatted for JSON
        json_string = json.dumps(prop_table_dict_list, ensure_ascii=False, indent=2)
        converted_list = json.loads(json_string)
    
        return converted_list
    
    except Exception as error:
        print(f"Error in convert_quotes_comprehensive: {str(error)}")
        # Fallback to manual conversion if JSON conversion fails
        return convert_single_quotes_to_double_quotes(prop_table_dict_list)


def fix_json_quotes_in_table_data(prop_table_dict_list):
    """
    Specifically designed to fix quote issues in table_data JSON structures.
    Handles the nested table_data format with proper JSON conversion.

    Args:
        prop_table_dict_list (list): List of property table dictionaries
    
    Returns:
        list: Fixed list with proper JSON formatting
    """
    try:
        fixed_list = []
    
        for prop_table_dict in prop_table_dict_list:
            fixed_dict = {}
        
            for property_name, table_entry in prop_table_dict.items():
                if isinstance(table_entry, dict):
                    fixed_entry = {}
                
                    # Handle each field in the table entry
                    for key, value in table_entry.items():
                        if key == 'table_data' and isinstance(value, list):
                            # Special handling for table_data which contains list of dictionaries
                            fixed_table_data = []
                            for row in value:
                                if isinstance(row, dict):
                                    # Convert each row dictionary properly
                                    fixed_row = {}
                                    for col_key, col_value in row.items():
                                        # Ensure string values are properly quoted
                                        if isinstance(col_value, str):
                                            fixed_row[str(col_key)] = col_value
                                        else:
                                            fixed_row[str(col_key)] = str(col_value)
                                    fixed_table_data.append(fixed_row)
                                else:
                                    fixed_table_data.append(row)
                            fixed_entry[key] = fixed_table_data
                        else:
                            # Handle other fields normally
                            fixed_entry[key] = value
                
                    fixed_dict[property_name] = fixed_entry
                else:
                    fixed_dict[property_name] = table_entry
        
            fixed_list.append(fixed_dict)
    
        # Final JSON conversion to ensure everything is properly formatted
        json_string = json.dumps(fixed_list, ensure_ascii=False)
        final_list = json.loads(json_string)
    
        return final_list
    
    except Exception as error:
        print(f"Error in fix_json_quotes_in_table_data: {str(error)}")
        return prop_table_dict_list

def simple_quote_converter(prop_table_dict_list):
    """
    Simple function to convert single quotes to double quotes using JSON serialization.

    Args:
        prop_table_dict_list (list): Input list with potential single quote issues
    
    Returns:
        list: List with proper double quotes
    """
    try:
        # Convert to JSON string (this automatically uses double quotes)
        json_str = json.dumps(prop_table_dict_list, ensure_ascii=False)
    
        # Convert back to Python object (now with proper quoting)
        converted_list = json.loads(json_str)
    
        return converted_list
    
    except Exception as error:
        print(f"Error in simple_quote_converter: {str(error)}")
        return prop_table_dict_list


def get_prop_data_by_llama3_model(tan_number, prop_table_dict_list, output_file):
    """
    Extract property data with model-specific handling.
    """
    # Fix input data quotes (this works well with LLaMA 3.2)
    # url = "http://172.27.7.85:5007/lamma_3_1_model"  # or lamma_3_2_model or mistral_model
    url= "http://172.27.7.85:5007/lamma_3_2_model"
    # url = "http://172.27.7.85:5007/mistral_model"
    if "lamma_3_2_model" in url:
        prop_table_dict_list = simple_quote_converter(prop_table_dict_list)
        print("Applied quote conversion for LLaMA 3.2...")

    # Convert coordinates
    converted_prop_table_dict_list = convert_table_coordinates_in_list(prop_table_dict_list)
    print("Converted coordinates for FITZ compatibility...")

    # Initialize an empty list to store the dataframes
    tan_df_list = []

    # Iterate over each property table dictionary (now with converted coordinates)
    for prop_table_dict in converted_prop_table_dict_list:
        # Extract metadata for each property
        table_metadata = {}
        for property_name, table_entry in prop_table_dict.items():
            if isinstance(table_entry, dict):
                table_metadata[property_name] = table_entry

        # Get the response from the LLaMA3 model
        response = get_response(url, prop_table_dict)
        validated_response = validate_extracted_data(response, prop_table_dict)  # Add validation
    
        df = extract_data_to_dataframe(validated_response, table_metadata)  # Use validated response

        # Check if the data is not empty
        if not df.empty:
            tan_df_list.append(df)

    # Concatenate the dataframes
    if tan_df_list:
        tan_df = pd.concat(tan_df_list, ignore_index=True)
        tan_df['tan_name'] = tan_number
    else:
        # Return an empty dataframe if no data was extracted with all expected columns
        tan_df = pd.DataFrame(columns=[
            'property_name', 'value', 'unit', 'compound', 'source_key', 'tan_name',
            'table_id', 'page_number', 'coord_left', 'coord_top', 'coord_right',
            'coord_bottom', 'coord_origin', 'page_width', 'page_height'
        ])

    # Convert all columns to string explicitly (if needed)
    tan_df = tan_df.astype(str)

    # Save to Excel while preserving data types as strings
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        tan_df.to_excel(writer, index=False)
    # Return the dataframe
    tan_df.drop_duplicates(inplace=True)
    return tan_df


def remove_duplicate_table_rows(prop_table_dict_list):
    """
    Remove duplicate rows from table data to prevent model confusion.

    Args:
        prop_table_dict_list (list): List of property table dictionaries
    
    Returns:
        list: Cleaned list with duplicate rows removed
    """
    try:
        cleaned_list = []
    
        for prop_table_dict in prop_table_dict_list:
            cleaned_dict = {}
        
            for property_name, table_entry in prop_table_dict.items():
                if isinstance(table_entry, dict):
                    cleaned_entry = table_entry.copy()
                    table_data = table_entry.get('table_data', [])
                
                    if table_data:
                        # Keep header row (first row)
                        header_row = table_data[0]
                        data_rows = table_data[1:]
                    
                        # Remove duplicate rows based on first column (compound name)
                        seen_compounds = set()
                        unique_rows = [header_row]
                    
                        for row in data_rows:
                            compound_key = row.get('0', '')  # First column
                            if compound_key not in seen_compounds:
                                seen_compounds.add(compound_key)
                                unique_rows.append(row)
                            else:
                                print(f"Removed duplicate row for compound: {compound_key}")
                    
                        cleaned_entry['table_data'] = unique_rows
                
                    cleaned_dict[property_name] = cleaned_entry
                else:
                    cleaned_dict[property_name] = table_entry
        
            cleaned_list.append(cleaned_dict)
    
        return cleaned_list
    
    except Exception as error:
        print(f"Error in remove_duplicate_table_rows: {str(error)}")
        return prop_table_dict_list


def safe_json_parse(response_text, property_name):
    """
    Safely parse JSON response with multiple fallback strategies.
    
    Args:
        response_text (str): Raw response text from model
        property_name (str): Property name for debugging
        
    Returns:
        list: Parsed JSON data or empty list
    """
    try:
        # Strategy 1: Direct parsing
        return json.loads(response_text)
    except json.JSONDecodeError:
        try:
            # Strategy 2: Clean and fix common issues
            cleaned_text = clean_json_response(response_text)
            return json.loads(cleaned_text)
        except json.JSONDecodeError:
            try:
                # Strategy 3: Fix missing commas
                fixed_text = fix_json_commas(response_text)
                return json.loads(fixed_text)
            except json.JSONDecodeError:
                print(f"All JSON parsing failed for {property_name}. Raw response: {response_text[:200]}...")
                return []


def clean_json_response(response_text):
    """
    Clean common JSON formatting issues.
    
    Args:
        response_text (str): Raw response text
        
    Returns:
        str: Cleaned JSON string
    """
    import re
    
    # Remove markdown code blocks
    response_text = response_text.strip()
    if response_text.startswith('```json'):
        response_text = response_text[7:]
    if response_text.startswith('```'):
        response_text = response_text[3:]
    if response_text.endswith('```'):
        response_text = response_text[:-3]
    
    # Remove explanatory text before JSON
    response_text = response_text.strip()
    
    # Find JSON array boundaries
    start_idx = response_text.find('[')
    end_idx = response_text.rfind(']')
    
    if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
        response_text = response_text[start_idx:end_idx + 1]
    
    return response_text.strip()


def fix_json_commas(response_text):
    """
    Fix missing commas in JSON response.
    
    Args:
        response_text (str): JSON text with potential comma issues
        
    Returns:
        str: Fixed JSON string
    """
    import re
    
    # Fix missing commas between objects
    response_text = re.sub(r'}\s*{', '}, {', response_text)
    
    # Fix missing commas between key-value pairs on new lines
    response_text = re.sub(r'"\s*\n\s*"', '", "', response_text)
    
    # Fix missing commas after values before new keys
    response_text = re.sub(r'"\s*"([^"]*)":', '", "\1":', response_text)
    
    # Remove trailing commas
    response_text = re.sub(r',\s*}', '}', response_text)
    response_text = re.sub(r',\s*]', ']', response_text)
    
    return response_text




if __name__ == "__main__":
    # Get the TAN number of pdf file
    tan_number = "41484277K"

    ### Get the property table data dictionary list
    # prop_table_dict_list  = [
    #     {'Cp': 'Table 1. Material properties at room temperature for heat transfer model.\nMaterial properties\nDensity\nSpecific heat\nThermal conductivity\nReference\n\x02 (kg/m3)\nCp (J/kg·°C)\nkxx, kyy\n(W/m\x04\x02C)\nkzz\n(W/m\x04\x02C)\nMetal mesh\n7780\n460\n10\n10\nRef. 28\nWood insulator\n1242\n1090\n0.12\n0.12\nRef. 28\nPEI resin film\n1270\n980\n0.22\n0.22\n\x02, k: Ref. 26; Cp: Ref. 29\nGF/PEI\n1930\n890\n0.53\n0.4\nMeasured\nGF/PEI:glass fibre/polyetherimide.'},

    #     {'Tg': 'Table S2. Thermal material properties of the BPURs. \nComposition \nTg, DMTA (°C) \nTg, DSC (°C) \nBPUR 10HS \n-31.7 ± 0.2*\n,X,^ \n-43.5 ± 0.7*\n, ǂ \nBPUR 30HS \n-28.9 ± 1.2*\n, \n-41.5 ± 0.5^ \nBPUR 50HS \n-27.2 ± 1.2 \nX, ǂ \n-41.1 ± 0.2\nǂ, X \nPCL-PUR 30HS \n-44.4 ± 2.1^\n, , ǂ \n-59.0 ± 1.0*\n,^\n,X'},
    # ]


    prop_table_dict_list  =[
    {'DS': {'table_id': 'table_3_1', 'title': 'Table 1 The DSC parameters of BA-a/AC at different AC wt% loading.', 'table_data': [{'0': 'Sample', '1': 'Initial exotherm ( /C176 C)', '2': 'Peak exotherm ( /C176 C)', '3': 'Heat of reaction (J/g)', '4': 'Heat of reaction (J/g)'}, {'0': 'BA-a', '1': '186', '2': '234.5', '3': '390.7', '4': '390.7'}, {'0': 'BA-a/AC1', '1': '179', '2': '230.4', '3': '375.8', '4': '375.8'}, {'0': 'BA-a/AC2', '1': '175', '2': '229.6', '3': '363.4', '4': '363.4'}, {'0': 'BA-a/AC3', '1': '172', '2': '227.5', '3': '323.9', '4': '323.9'}, {'0': 'BA-a/AC4', '1': '167', '2': '224.6', '3': '302.5', '4': '302.5'}, {'0': 'BA-a/AC5', '1': '164', '2': '224.1', '3': '322.5', '4': '322.5'}, {'0': 'BA-a/AC5', '1': '164', '2': '224.1', '3': '322.5', '4': '322.5'}], 'page_number': 3, 'coordinates': {'left': 32.093475341796875, 'top': 449.9383239746094, 'right': 284.178466796875, 'bottom': 371.1972351074219, 'coord_origin': 'BOTTOMLEFT'}, 'page_size': {'width': 595.2760009765625, 'height': 793.7009887695312}}},
    
    {'tensile stress': {'table_id': 'table_4_2', 'title': 'Tensile, impact and brittleness parameters for pristine poly(BA-a) and AC filled composites.', 'table_data': [{'0': 'Sample', '1': 'Tensile Stress', '2': '(MPa)', '3': 'Young modulus (GPa)', '4': 'Impact strength (kJ/m 2 )', '5': 'Brittleness ( B ) (% Pa/10 10 )', '6': 'Brittleness ( B ) (% Pa/10 10 )'}, {'0': 'P(BA-a)', '1': '27.93 ± 0.78', '2': '27.93 ± 0.78', '3': '2.16 ± 0.122', '4': '1.4 ± 0.12', '5': '3.26', '6': '3.26'}, {'0': 'P(BA-a/AC1)', '1': '32.49 ± 1.21', '2': '32.49 ± 1.21', '3': '2.56 ± 0.191', '4': '2.7 ± 0.12', '5': '2.74', '6': '2.74'}, {'0': 'P(BA-a/AC2)', '1': '39.55 ± 1.53', '2': '39.55 ± 1.53', '3': '2.84 ± 0.182', '4': '3.9 ± 0.14', '5': '2.50', '6': '2.50'}, {'0': 'P(BA-a/AC3)', '1': '43.03 ± 1.12', '2': '43.03 ± 1.12', '3': '3.10 ± 0.175', '4': '4.7 ± 0.22', '5': '2.35', '6': '2.35'}, {'0': 'P(BA-a/AC4)', '1': '44.84 ± 1.31', '2': '44.84 ± 1.31', '3': '3.31 ± 0.205', '4': '5.7 ± 0.21', '5': '2.15', '6': '2.15'}, {'0': 'P(BA-a/AC5)', '1': '48.82 ± 1.19', '2': '48.82 ± 1.19', '3': '3.54 ± 0.174', '4': '5.2 ± 0.16', '5': '2.12', '6': '2.12'}], 'page_number': 4, 'coordinates': {'left': 42.0387077331543, 'top': 707.4191436767578, 'right': 562.8633422851562, 'bottom': 637.2298889160156, 'coord_origin': 'BOTTOMLEFT'}, 'page_size': {'width': 595.2760009765625, 'height': 793.7009887695312}}}, 
    
    {'Young modulus': {'table_id': 'table_4_2', 'title': 'Tensile, impact and brittleness parameters for pristine poly(BA-a) and AC filled composites.', 'table_data': [{'0': 'Sample', '1': 'Tensile Stress', '2': '(MPa)', '3': 'Young modulus (GPa)', '4': 'Impact strength (kJ/m 2 )', '5': 'Brittleness ( B ) (% Pa/10 10 )', '6': 'Brittleness ( B ) (% Pa/10 10 )'}, {'0': 'P(BA-a)', '1': '27.93 ± 0.78', '2': '27.93 ± 0.78', '3': '2.16 ± 0.122', '4': '1.4 ± 0.12', '5': '3.26', '6': '3.26'}, {'0': 'P(BA-a/AC1)', '1': '32.49 ± 1.21', '2': '32.49 ± 1.21', '3': '2.56 ± 0.191', '4': '2.7 ± 0.12', '5': '2.74', '6': '2.74'}, {'0': 'P(BA-a/AC2)', '1': '39.55 ± 1.53', '2': '39.55 ± 1.53', '3': '2.84 ± 0.182', '4': '3.9 ± 0.14', '5': '2.50', '6': '2.50'}, {'0': 'P(BA-a/AC3)', '1': '43.03 ± 1.12', '2': '43.03 ± 1.12', '3': '3.10 ± 0.175', '4': '4.7 ± 0.22', '5': '2.35', '6': '2.35'}, {'0': 'P(BA-a/AC4)', '1': '44.84 ± 1.31', '2': '44.84 ± 1.31', '3': '3.31 ± 0.205', '4': '5.7 ± 0.21', '5': '2.15', '6': '2.15'}, {'0': 'P(BA-a/AC5)', '1': '48.82 ± 1.19', '2': '48.82 ± 1.19', '3': '3.54 ± 0.174', '4': '5.2 ± 0.16', '5': '2.12', '6': '2.12'}], 'page_number': 4, 'coordinates': {'left': 42.0387077331543, 'top': 707.4191436767578, 'right': 562.8633422851562, 'bottom': 637.2298889160156, 'coord_origin': 'BOTTOMLEFT'}, 'page_size': {'width': 595.2760009765625, 'height': 793.7009887695312}}}] 

    output_file =f"{tan_number}_prop_data.xlsx"

    # Get the property data from the LLaMA3 model using the TAN number and property table dictionary list
    prop_df = get_prop_data_by_llama3_model(tan_number, prop_table_dict_list, output_file)

    # Save the property data to an Excel file
    prop_df.to_excel(output_file, index=False)

    print(prop_df.to_markdown())
    print(prop_df.shape)
####################################################################
