"""
Optimized module for extracting physical property data from JSON table structures using LLaMA models.

This module processes compound property data from structured JSON tables and extracts:
- Compound names
- Physical property values
- Units
- Metadata (coordinates, page numbers, etc.)

Key Features:
- Direct JSON processing (no string conversion)
- Comprehensive compound name extraction
- Extensive prompts to prevent model hallucination
- FITZ coordinate conversion support
- Robust error handling and validation

Date: 10-09-2025
@author: <PERSON>
"""

import requests
import json
import pandas as pd
import re



def convert_coordinates_for_fitz(coordinates, page_size, coord_origin):
    """Convert coordinates from BOTTOMLEFT to TOPLEFT for FITZ compatibility."""
    try:
        if coord_origin.upper() == 'TOPLEFT':
            return {
                'left': coordinates.get('left', 0),
                'top': coordinates.get('top', 0),
                'right': coordinates.get('right', 0),
                'bottom': coordinates.get('bottom', 0),
                'coord_origin': 'TOPLEFT'
            }
        elif coord_origin.upper() == 'BOTTOMLEFT':
            page_height = page_size.get('height', 0)
            return {
                'left': coordinates.get('left', 0),
                'top': page_height - coordinates.get('bottom', 0),
                'right': coordinates.get('right', 0),
                'bottom': page_height - coordinates.get('top', 0),
                'coord_origin': 'TOPLEFT'
            }
        else:
            return coordinates
    except Exception as error:
        print(f"Coordinate conversion error: {error}")
        return coordinates



def convert_table_coordinates_in_list(prop_table_dict_list):
    """Convert coordinates for all tables to FITZ-compatible format."""
    try:
        for prop_table_dict in prop_table_dict_list:
            for property_name, table_entry in prop_table_dict.items():
                if isinstance(table_entry, dict):
                    coordinates = table_entry.get('coordinates', {})
                    page_size = table_entry.get('page_size', {})
                    coord_origin = coordinates.get('coord_origin', 'TOPLEFT')

                    converted_coords = convert_coordinates_for_fitz(coordinates, page_size, coord_origin)
                    table_entry['coordinates'] = converted_coords

        return prop_table_dict_list
    except Exception as error:
        print(f"Coordinate conversion error: {error}")
        return prop_table_dict_list



def get_llama_response(url, prop_table_dict):
    """
    Send optimized requests to LLaMA model with comprehensive prompts for compound property extraction.

    Args:
        url (str): LLaMA model API endpoint
        prop_table_dict (dict): Property name to table entry mapping

    Returns:
        dict: Extracted property data responses
    """
    headers = {'Content-Type': 'application/json'}
    responses = {}

    for property_name, table_entry in prop_table_dict.items():
        table_title = table_entry.get('title', '') if isinstance(table_entry, dict) else ''
        table_data_json = table_entry.get('table_data', []) if isinstance(table_entry, dict) else table_entry

        # Comprehensive prompt with extensive examples to prevent hallucination
        prompt = f"""You are an Expert DATA CURATOR specializing in Physics and Chemistry data extraction. Extract compound property data from structured JSON tables.

            CRITICAL INSTRUCTIONS:
            1. Extract ONLY data for property '{property_name}'
            2. Find compound names from the first column (column '0')
            3. Extract property values from the matching column
            4. Determine units from column headers or domain knowledge
            5. Return ONLY valid JSON array format

            COMPLETE EXAMPLE 1:
            Input Property: 'tensile strength'
            Table Title: 'Mechanical properties of polymer composites'
            Table Data:
            [
            {{"0": "Sample", "1": "Tensile Strength (MPa)", "2": "Young Modulus (GPa)", "3": "Elongation (%)"}},
            {{"0": "Pure Polymer", "1": "45.2 ± 1.1", "2": "2.8 ± 0.15", "3": "12.5 ± 0.8"}},
            {{"0": "Composite-A", "1": "52.7 ± 1.3", "2": "3.2 ± 0.18", "3": "10.8 ± 0.6"}},
            {{"0": "Composite-B", "1": "48.9 ± 0.9", "2": "3.0 ± 0.12", "3": "11.2 ± 0.7"}}
            ]

            Expected Output:
            [
            {{"property_name": "tensile strength", "value": "45.2 ± 1.1", "unit": "MPa", "compound": "Pure Polymer"}},
            {{"property_name": "tensile strength", "value": "52.7 ± 1.3", "unit": "MPa", "compound": "Composite-A"}},
            {{"property_name": "tensile strength", "value": "48.9 ± 0.9", "unit": "MPa", "compound": "Composite-B"}}
            ]

            COMPLETE EXAMPLE 2:
            Input Property: 'melting point'
            Table Title: 'Thermal properties of organic compounds'
            Table Data:
            [
            {{"0": "Compound", "1": "Melting Point (°C)", "2": "Boiling Point (°C)", "3": "Density (g/cm³)"}},
            {{"0": "Benzene", "1": "5.5", "2": "80.1", "3": "0.876"}},
            {{"0": "Toluene", "1": "-95.0", "2": "110.6", "3": "0.867"}},
            {{"0": "Phenol", "1": "40.5", "2": "181.7", "3": "1.071"}}
            ]

            Expected Output:
            [
            {{"property_name": "melting point", "value": "5.5", "unit": "°C", "compound": "Benzene"}},
            {{"property_name": "melting point", "value": "-95.0", "unit": "°C", "compound": "Toluene"}},
            {{"property_name": "melting point", "value": "40.5", "unit": "°C", "compound": "Phenol"}}
            ]

            COMPOUND NAME EXTRACTION RULES:
            - Always use the value from column '0' as the compound name
            - Include full compound names with prefixes/suffixes (e.g., "P(BA-a/AC1)", "Composite-A")
            - Do not abbreviate or modify compound names
            - If compound name contains special characters, keep them exactly as shown

            UNIT EXTRACTION RULES:
            - Extract units from column headers in parentheses: "(MPa)", "(°C)", "(g/cm³)"
            - Common units by property type:
            * Mechanical: MPa, GPa, Pa, N/m², kg/m³
            * Thermal: °C, K, J/g, cal/g, W/m·K
            * Electrical: Ω, S/m, V, A
            * Chemical: mol/L, pH, ppm, %
            - If no unit found, use empty string ""

            VALUE EXTRACTION RULES:
            - Keep exact numerical format: "45.2 ± 1.1", "2.5-3.0", "101.0"
            - Include uncertainty symbols: ±, -, ~
            - Do not remove decimal places
            - Do not convert units or modify values

            JSON FORMAT REQUIREMENTS:
            - Use double quotes only
            - No trailing commas
            - Valid JSON array format
            - Four required keys: property_name, value, unit, compound

            YOUR TASK:
            Extract data for property '{property_name}' from this table:

            TABLE TITLE: {table_title}

            TABLE DATA:
            {json.dumps(table_data_json, indent=2)}

            Return ONLY the JSON array (no explanations):"""

        try:
            payload = json.dumps({"prompt": prompt})
            response = requests.post(url, headers=headers, data=payload)

            # Parse response with error handling
            response_text = response.text.strip()
            parsed_data = parse_llama_response(response_text, property_name)
            responses[property_name] = parsed_data

        except Exception as e:
            print(f"Error processing {property_name}: {e}")
            responses[property_name] = []

    return responses


def parse_llama_response(response_text, property_name):
    """Parse LLaMA response with robust error handling."""
    try:
        # Clean response text
        response_text = response_text.strip()

        # Remove markdown code blocks if present
        if response_text.startswith('```'):
            response_text = response_text.split('```')[1]
            if response_text.startswith('json'):
                response_text = response_text[4:]

        # Try direct JSON parsing
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            # Try to extract JSON array from text
            import re
            json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            else:
                print(f"Failed to parse response for {property_name}")
                return []

    except Exception as e:
        print(f"Error parsing response for {property_name}: {e}")
        return []


def get_response_1(url, prop_table_dict):
    """
    Sends a POST request with enhanced example-based prompt.
    """
    headers = {'Content-Type': 'application/json'}
    responses = {}

    # Determine model type from URL for specific handling
    model_type = "unknown"
    if "lamma_3_1_model" in url:
        model_type = "llama3.1"
    elif "lamma_3_2_model" in url:
        model_type = "llama3.2"
    elif "mistral_model" in url:
        model_type = "mistral"

    for property_name, table_entry in prop_table_dict.items():
        table_title = table_entry.get('title', '') if isinstance(table_entry, dict) else ''
        table_data_json = table_entry.get('table_data', []) if isinstance(table_entry, dict) else table_entry

        # Enhanced prompt with complete example
        prompt = (
            "You are an Expert DATA CURATOR with domain knowledge of Physics and Chemistry. You excel at extracting structured data from JSON table format.\n\n"

            "COMPLETE EXAMPLE:\n\n"

            "EXAMPLE INPUT:\n"
            "Property to extract: 'tensile strength'\n"
            "Table Title: 'Table 1. Mechanical properties of polymer composites'\n"
            "Table Data:\n"
            "[\n"
            "  {'0': 'Sample', '1': 'Tensile Strength (MPa)', '2': 'Young Modulus (GPa)', '3': 'Elongation (%)'},\n"
            "  {'0': 'Pure Polymer', '1': '45.2 ± 1.1', '2': '2.8 ± 0.15', '3': '12.5 ± 0.8'},\n"
            "  {'0': 'Composite-1', '1': '52.7 ± 1.3', '2': '3.2 ± 0.18', '3': '10.8 ± 0.6'},\n"
            "  {'0': 'Composite-2', '1': '48.9 ± 0.9', '2': '3.0 ± 0.12', '3': '11.2 ± 0.7'}\n"
            "]\n\n"

            "EXAMPLE OUTPUT for 'tensile strength':\n"
            "[\n"
            "  {\"property_name\": \"tensile strength\", \"value\": \"45.2 ± 1.1\", \"unit\": \"MPa\", \"compound\": \"Pure Polymer\"},\n"
            "  {\"property_name\": \"tensile strength\", \"value\": \"52.7 ± 1.3\", \"unit\": \"MPa\", \"compound\": \"Composite-1\"},\n"
            "  {\"property_name\": \"tensile strength\", \"value\": \"48.9 ± 0.9\", \"unit\": \"MPa\", \"compound\": \"Composite-2\"}\n"
            "]\n\n"

            "EXPLANATION OF EXAMPLE:\n"
            "1. Found column '1' with header 'Tensile Strength (MPa)' - matches requested property\n"
            "2. Extracted unit 'MPa' from column header\n"
            "3. Extracted values from column '1': '45.2 ± 1.1', '52.7 ± 1.3', '48.9 ± 0.9'\n"
            "4. Matched compounds from column '0': 'Pure Polymer', 'Composite-1', 'Composite-2'\n"
            "5. Used requested property name 'tensile strength' for all entries\n\n"

            "CRITICAL JSON FORMAT REQUIREMENTS:\n"
            "- Use ONLY double quotes (\") for all strings\n"
            "- NO single quotes (') anywhere in the response\n"
            "- Return ONLY valid JSON array format\n"
            "- NO markdown code blocks or explanations\n"
            "- NO trailing commas\n"
            "- Keep numerical values exactly as they appear (with ± symbols, ranges, etc.)\n\n"

            f"YOUR TASK: Extract ONLY data for property '{property_name}' from the table below.\n\n"

            "STEP-BY-STEP PROCESS:\n"
            f"1. Find the column that contains '{property_name}' data (check headers carefully)\n"
            f"2. Extract ONLY values from that specific column (ignore other columns)\n"
            "3. Get compound/sample names from the first column (column '0')\n"
            "4. Extract unit from the matched column header or use domain knowledge\n"
            f"5. Use '{property_name}' as property_name for all entries\n"
            "6. Return valid JSON array with double quotes only\n\n"

            f"INPUT DATA:\n"
            f"Property to extract: '{property_name}'\n"
            f"Table Title: {table_title}\n"
            f"Table Data:\n{json.dumps(table_data_json, indent=2)}\n\n"

            "RESPOND WITH VALID JSON ARRAY ONLY (no explanations, no markdown):"
        )

        payload = json.dumps({"prompt": prompt})
        response = requests.post(url, headers=headers, data=payload)

        # Model-specific response parsing
        try:
            response_text = response.text.strip()
            print(f"Raw response for {property_name} ({model_type}): {response_text[:200]}...")

            parsed_response = parse_model_response(response_text, model_type, property_name)
            responses[property_name] = parsed_response

        except Exception as e:
            print(f"Error processing response for property {property_name}: {e}")
            responses[property_name] = []

    return responses

def get_response_0(url, prop_table_dict):
    """
    Sends a POST request to the specified URL and retrieves property data from the response.
    Enhanced with better JSON parsing.
    """
    headers = {'Content-Type': 'application/json'}
    responses = {}

    for property_name, table_entry in prop_table_dict.items():
        # Use JSON data directly - more efficient and accurate
        table_title = table_entry.get('title', '') if isinstance(table_entry, dict) else ''
        table_data_json = table_entry.get('table_data', []) if isinstance(table_entry, dict) else table_entry

        prompt = (
            "You are an Expert DATA CURATOR with domain knowledge of Physics and Chemistry. You excel at extracting structured data from JSON table format.\n\n"

            "EXAMPLE INPUT FORMAT:\n"
            "{\n"
            "  'title': 'Table 1. Elemental analysis and meff of ligands and Zn complexes',\n"
            "  'table_data': [\n"
            "    {'0': 'Ligand/Complex', '1': 'M:L', '2': 'meff (B.M.)', '3': 'Conductance'},\n"
            "    {'0': 'L1', '1': '1:2', '2': '0.09', '3': '15.2'},\n"
            "    {'0': 'L2', '1': '1:2', '2': '0.07', '3': '18.5'}\n"
            "  ]\n"
            "}\n\n"

            "EXPECTED OUTPUT for property 'meff':\n"
            "[\n"
            "  {\"property_name\": \"meff\", \"value\": \"0.09\", \"unit\": \"B.M.\", \"compound\": \"L1\"},\n"
            "  {\"property_name\": \"meff\", \"value\": \"0.07\", \"unit\": \"B.M.\", \"compound\": \"L2\"}\n"
            "]\n\n"

            f"TASK: Extract all data for property '{property_name}' from the structured table below.\n\n"

            "INSTRUCTIONS:\n"
            f"1. Find all columns containing '{property_name}' data (check column headers and values)\n"
            "2. Extract ALL instances of the property from multiple columns if present\n"
            "3. For each data point, determine the corresponding compound/sample from the first column\n"
            "4. Identify the unit from column headers or use domain knowledge\n"
            "5. Keep numerical ranges as-is (e.g., '2.5-3.0' stays '2.5-3.0')\n"
            "6. Extract only numerical values (remove units from value field)\n"
            "7. Maintain data format exactly as provided (don't change '101.0' to '101')\n"
            "8. Include repeated data if present\n\n"

            f"REQUIREMENTS:\n"
            f"- property_name: Always use '{property_name}'\n"
            "- value: Only numerical data (no units)\n"
            "- unit: Consistent unit for all entries (empty string if unitless)\n"
            "- compound: Sample/compound name from context\n\n"

            "CRITICAL: Return ONLY valid JSON array with proper commas between objects.\n"
            "Format: [{\"property_name\": \"X\", \"value\": \"Y\", \"unit\": \"Z\", \"compound\": \"W\"}]\n\n"

            f"TABLE TITLE: {table_title}\n\n"
            f"TABLE DATA (JSON):\n{json.dumps(table_data_json, indent=2)}\n"
        )

        payload = json.dumps({"prompt": prompt})

        # Send a POST request to the specified URL
        response = requests.post(url, headers=headers, data=payload)

        # Enhanced JSON parsing with error handling
        try:
            response_text = response.text.strip()
            parsed_response = safe_json_parse(response_text, property_name)
            responses[property_name] = parsed_response
            
        except Exception as e:
            print(f"Error processing response for property {property_name}: {e}")
            responses[property_name] = []

    return responses

def get_response(url, prop_table_dict):
    """
    Sends a POST request with enhanced example-based prompt.
    """
    headers = {'Content-Type': 'application/json'}
    responses = {}

    # Determine model type from URL for specific handling
    model_type = "unknown"
    if "lamma_3_1_model" in url:
        model_type = "llama3.1"
    elif "lamma_3_2_model" in url:
        model_type = "llama3.2"
    elif "mistral_model" in url:
        model_type = "mistral"

    for property_name, table_entry in prop_table_dict.items():
        table_title = table_entry.get('title', '') if isinstance(table_entry, dict) else ''
        table_data_json = table_entry.get('table_data', []) if isinstance(table_entry, dict) else table_entry

        # Enhanced prompt with complete example
        prompt = (
            "You are an Expert DATA CURATOR with domain knowledge of Physics and Chemistry. You excel at extracting structured data from a table given in JSON  format.\n\n"

            "COMPLETE EXAMPLE:\n\n"

            "EXAMPLE INPUT:\n"
            "Property to extract: 'tensile strength'\n"
            "Table Title: 'Table 1. Mechanical properties of polymer composites'\n"
            "Table Data:\n"
            "[\n"
            "  {'0': 'Sample', '1': 'Tensile Strength (MPa)', '2': 'Young Modulus (GPa)', '3': 'Elongation (%)'},\n"
            "  {'0': 'Pure Polymer', '1': '45.2 ± 1.1', '2': '2.8 ± 0.15', '3': '12.5 ± 0.8'},\n"
            "  {'0': 'Composite-1', '1': '52.7 ± 1.3', '2': '3.2 ± 0.18', '3': '10.8 ± 0.6'},\n"
            "  {'0': 'Composite-2', '1': '48.9 ± 0.9', '2': '3.0 ± 0.12', '3': '11.2 ± 0.7'}\n"
            "]\n\n"

            "EXAMPLE OUTPUT for 'tensile strength':\n"
            "[\n"
            "  {\"property_name\": \"tensile strength\", \"value\": \"45.2 ± 1.1\", \"unit\": \"MPa\", \"compound\": \"Pure Polymer\"},\n"
            "  {\"property_name\": \"tensile strength\", \"value\": \"52.7 ± 1.3\", \"unit\": \"MPa\", \"compound\": \"Composite-1\"},\n"
            "  {\"property_name\": \"tensile strength\", \"value\": \"48.9 ± 0.9\", \"unit\": \"MPa\", \"compound\": \"Composite-2\"}\n"
            "]\n\n"

            "EXPLANATION OF EXAMPLE:\n"
            "1. Found column '1' with header 'Tensile Strength (MPa)' - matches requested property\n"
            "2. Extracted unit 'MPa' from column header\n"
            "3. Extracted values from column '1': '45.2 ± 1.1', '52.7 ± 1.3', '48.9 ± 0.9'\n"
            "4. Matched compounds from column '0': 'Pure Polymer', 'Composite-1', 'Composite-2'\n"
            "5. Used requested property name 'tensile strength' for all entries\n\n"

            "CRITICAL JSON FORMAT REQUIREMENTS:\n"
            "- Use ONLY double quotes (\") for all strings\n"
            "- NO single quotes (') anywhere in the response\n"
            "- Return ONLY valid JSON array format\n"
            "- NO markdown code blocks or explanations\n"
            "- NO trailing commas\n"
            "- Keep numerical values exactly as they appear (with ± symbols, ranges, etc.)\n\n"

            f"YOUR TASK: Extract ONLY data for property '{property_name}' from the table below.\n\n"

            "STEP-BY-STEP PROCESS:\n"
            f"1. Find the column that contains '{property_name}' data (check headers carefully)\n"
            f"2. Extract ONLY values from that specific column (ignore other columns)\n"
            "3. Get compound/sample names from the first column (column '0')\n"
            "4. Extract unit from the matched column header or use domain knowledge\n"
            f"5. Use '{property_name}' as property_name for all entries\n"
            "6. Return valid JSON array with double quotes only\n\n"

            f"INPUT DATA:\n"
            f"Property to extract: '{property_name}'\n"
            f"Table Title: {table_title}\n"
            f"Table Data:\n{json.dumps(table_data_json, indent=2)}\n\n"

            "RESPOND WITH VALID JSON ARRAY ONLY (no explanations, no markdown):"
        )

        payload = json.dumps({"prompt": prompt})
        response = requests.post(url, headers=headers, data=payload)

        # Model-specific response parsing
        try:
            response_text = response.text.strip()
            print(f"Raw response for {property_name} ({model_type}): {response_text[:200]}...")

            parsed_response = parse_model_response(response_text, model_type, property_name)
            responses[property_name] = parsed_response

        except Exception as e:
            print(f"Error processing response for property {property_name}: {e}")
            responses[property_name] = []

    return responses



def parse_model_response(response_text, model_type, property_name):
    """
    Enhanced model response parser with specific fixes for comma delimiter issues.

    Args:
        response_text (str): Raw response from model
        model_type (str): Type of model (llama3.1, llama3.2, mistral)
        property_name (str): Property being extracted

    Returns:
        list: Parsed JSON data
    """
    try:
        # Strategy 1: Direct JSON parsing
        return json.loads(response_text)

    except json.JSONDecodeError as e:
        print(f"Direct JSON parsing failed for {model_type}: {e}")

        try:
            # Strategy 2: Fix comma delimiter issues specifically
            comma_fixed_response = fix_comma_delimiter_issues(response_text)
            return json.loads(comma_fixed_response)

        except json.JSONDecodeError as e:
            print(f"Comma fixing failed for {model_type}: {e}")

            try:
                # Strategy 3: Clean and fix common issues
                cleaned_response = clean_model_response(response_text, model_type)
                return json.loads(cleaned_response)

            except json.JSONDecodeError as e:
                print(f"Cleaned JSON parsing failed for {model_type}: {e}")

                try:
                    # Strategy 4: Aggressive quote and comma fixing
                    fixed_response = fix_quotes_and_commas_aggressively(response_text)
                    return json.loads(fixed_response)

                except json.JSONDecodeError as e:
                    print(f"Aggressive fixing failed for {model_type}: {e}")

                    try:
                        # Strategy 5: Manual extraction as last resort
                        return extract_data_manually(response_text, property_name)

                    except Exception as e:
                        print(f"Manual extraction failed for {model_type}: {e}")
                        return []


def fix_comma_delimiter_issues(response_text):
    """
    Specifically fix comma delimiter issues that cause JSON parsing to fail.

    Args:
        response_text (str): Raw response text with potential comma issues

    Returns:
        str: Fixed response text with proper comma delimiters
    """
    try:
        # Remove markdown code blocks first
        response_text = response_text.strip()
        if response_text.startswith('```json'):
            response_text = response_text[7:]
        elif response_text.startswith('```'):
            response_text = response_text[3:]
        if response_text.endswith('```'):
            response_text = response_text[:-3]

        response_text = response_text.strip()

        # Find the JSON array boundaries
        start_idx = response_text.find('[')
        end_idx = response_text.rfind(']')

        if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
            response_text = response_text[start_idx:end_idx + 1]

        # Fix common comma delimiter issues
        # 1. Missing commas between objects
        response_text = re.sub(r'}\s*{', '}, {', response_text)

        # 2. Missing commas between key-value pairs
        response_text = re.sub(r'"\s*"([^"]*)":', '", "\1":', response_text)

        # 3. Missing commas after values before new keys
        response_text = re.sub(r'"\s*\n\s*"', '", "', response_text)

        # 4. Fix missing commas in arrays
        response_text = re.sub(r'"\s*\n\s*{', '", {', response_text)

        # 5. Remove trailing commas that cause issues
        response_text = re.sub(r',\s*}', '}', response_text)
        response_text = re.sub(r',\s*]', ']', response_text)

        # 6. Fix double commas
        response_text = re.sub(r',,+', ',', response_text)

        # 7. Fix spaces around colons and commas
        response_text = re.sub(r'\s*:\s*', ': ', response_text)
        response_text = re.sub(r'\s*,\s*', ', ', response_text)

        return response_text.strip()

    except Exception as e:
        print(f"Error in fix_comma_delimiter_issues: {e}")
        return response_text


def fix_quotes_and_commas_aggressively(response_text):
    """
    Aggressively fix both quote and comma issues as a comprehensive solution.

    Args:
        response_text (str): Raw response text

    Returns:
        str: Fixed response text
    """
    try:
        # First apply comma fixes
        response_text = fix_comma_delimiter_issues(response_text)

        # Then apply quote fixes
        response_text = fix_quotes_aggressively(response_text)

        # Additional comprehensive fixes
        # Fix malformed JSON structures
        response_text = re.sub(r'"\s*:\s*"([^"]*)"([^,}\]]*)', r'": "\1"\2', response_text)

        # Fix missing quotes around property names
        response_text = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', response_text)

        # Fix missing quotes around string values
        response_text = re.sub(r':\s*([a-zA-Z][^,}\]]*?)([,}\]])', r': "\1"\2', response_text)

        # Final cleanup
        response_text = re.sub(r'\s+', ' ', response_text)  # Normalize whitespace
        response_text = response_text.strip()

        return response_text

    except Exception as e:
        print(f"Error in fix_quotes_and_commas_aggressively: {e}")
        return response_text


def clean_model_response(response_text, model_type):
            """
            Clean model response based on model type.

            Args:
                response_text (str): Raw response text
                model_type (str): Model type
    
            Returns:
                str: Cleaned response text
            """
            # Remove markdown code blocks
            response_text = response_text.strip()

            # Remove common markdown patterns
            patterns_to_remove = [
                r'\s*',
                r'\s*',
                r'^json\s*',
                r'Here.*?:\s*',
                r'The.*?is:\s*',
                r'Response:\s*',
                r'Output:\s*'
            ]

            for pattern in patterns_to_remove:
                response_text = re.sub(pattern, '', response_text, flags=re.IGNORECASE | re.MULTILINE)

            response_text = response_text.strip()

            # Model-specific cleaning
            if model_type == "llama3.1":
                # LLaMA 3.1 specific issues
                response_text = fix_llama31_issues(response_text)
            elif model_type == "mistral":
                # Mistral specific issues
                response_text = fix_mistral_issues(response_text)

            return response_text


def fix_llama31_issues(response_text):
            """
            Fix common LLaMA 3.1 response issues.
            """
            # LLaMA 3.1 often uses single quotes or has formatting issues
            # Replace single quotes with double quotes, but be careful with contractions

            # First, protect contractions
            contractions = re.findall(r"\b\w+'\w+\b", response_text)
            temp_response = response_text

            for i, contraction in enumerate(contractions):
                placeholder = f"__CONTRACTION_{i}__"
                temp_response = temp_response.replace(contraction, placeholder, 1)

            # Fix property names and values with single quotes
            temp_response = re.sub(r"'([^']*?)'(\s*:)", r'"\1"\2', temp_response)  # Keys
            temp_response = re.sub(r":\s*'([^']*?)'", r': "\1"', temp_response)    # Values
            temp_response = re.sub(r"\[\s*'([^']*?)'", r'["\1"', temp_response)    # Array start
            temp_response = re.sub(r",\s*'([^']*?)'", r', "\1"', temp_response)    # Array items

            # Restore contractions
            for i, contraction in enumerate(contractions):
                placeholder = f"__CONTRACTION_{i}__"
                temp_response = temp_response.replace(placeholder, contraction)

            return temp_response


def fix_mistral_issues(response_text):
            """
            Fix common Mistral response issues.
            """
            # Mistral often has similar issues to LLaMA 3.1
            return fix_llama31_issues(response_text)


def fix_quotes_aggressively(response_text):
            """
            Aggressively fix quote issues as last resort.
            """
            # This is a more aggressive approach - use with caution
            import re

            # Replace all single quotes with double quotes
            # This might break some strings, but it's a last resort
            response_text = response_text.replace("'", '"')

            # Fix common issues that might arise from aggressive replacement
            response_text = re.sub(r'"\s*(\w+)\s*"(\s*:)', r'"\1"\2', response_text)  # Fix spaced keys

            return response_text


def extract_data_manually(response_text, property_name):
    """
    Enhanced manual extraction when JSON parsing completely fails.
    Uses multiple pattern matching strategies.
    """
    try:
        result = []

        # Strategy 1: Look for complete object patterns
        object_pattern = r'\{[^}]*property_name[^}]*value[^}]*unit[^}]*compound[^}]*\}'
        objects = re.findall(object_pattern, response_text, re.IGNORECASE | re.DOTALL)

        for obj in objects:
            try:
                # Extract individual fields from each object
                prop_match = re.search(r'property_name["\s:]*["\']?([^"\',:}]+)["\']?', obj, re.IGNORECASE)
                value_match = re.search(r'value["\s:]*["\']?([^"\',:}]+)["\']?', obj, re.IGNORECASE)
                unit_match = re.search(r'unit["\s:]*["\']?([^"\',:}]*)["\']?', obj, re.IGNORECASE)
                compound_match = re.search(r'compound["\s:]*["\']?([^"\',:}]+)["\']?', obj, re.IGNORECASE)

                if prop_match and value_match and compound_match:
                    result.append({
                        "property_name": prop_match.group(1).strip(),
                        "value": value_match.group(1).strip(),
                        "unit": unit_match.group(1).strip() if unit_match else "",
                        "compound": compound_match.group(1).strip()
                    })
            except Exception as e:
                print(f"Error extracting from object: {e}")
                continue

        # Strategy 2: If no complete objects found, try line-by-line extraction
        if not result:
            lines = response_text.split('\n')
            current_obj = {}

            for line in lines:
                line = line.strip()
                if 'property_name' in line.lower():
                    match = re.search(r'property_name["\s:]*["\']?([^"\',:}]+)["\']?', line, re.IGNORECASE)
                    if match:
                        current_obj['property_name'] = match.group(1).strip()

                elif 'value' in line.lower() and 'property_name' not in line.lower():
                    match = re.search(r'value["\s:]*["\']?([^"\',:}]+)["\']?', line, re.IGNORECASE)
                    if match:
                        current_obj['value'] = match.group(1).strip()

                elif 'unit' in line.lower():
                    match = re.search(r'unit["\s:]*["\']?([^"\',:}]*)["\']?', line, re.IGNORECASE)
                    if match:
                        current_obj['unit'] = match.group(1).strip()

                elif 'compound' in line.lower():
                    match = re.search(r'compound["\s:]*["\']?([^"\',:}]+)["\']?', line, re.IGNORECASE)
                    if match:
                        current_obj['compound'] = match.group(1).strip()

                        # If we have all required fields, add to result
                        if len(current_obj) >= 3:  # property_name, value, compound minimum
                            if 'unit' not in current_obj:
                                current_obj['unit'] = ""
                            result.append(current_obj.copy())
                            current_obj = {}

        # Strategy 3: Simple regex patterns as fallback
        if not result:
            # Look for any numerical values that might be property values
            value_pattern = r'["\']?([0-9]+\.?[0-9]*\s*[±]?\s*[0-9]*\.?[0-9]*)["\']?'
            compound_pattern = r'["\']?([A-Za-z][A-Za-z0-9\(\)/\-]*)["\']?'

            values = re.findall(value_pattern, response_text)
            compounds = re.findall(compound_pattern, response_text)

            # Try to pair values with compounds
            for i, value in enumerate(values[:3]):  # Limit to first 3 matches
                compound = compounds[i] if i < len(compounds) else f"Unknown_{i+1}"
                result.append({
                    "property_name": property_name,
                    "value": value.strip(),
                    "unit": "",
                    "compound": compound.strip()
                })

        print(f"Manual extraction found {len(result)} items for {property_name}")
        return result

    except Exception as e:
        print(f"Manual extraction failed: {e}")
        return []

def validate_extracted_data(response, prop_table_dict):
    """
    Enhanced validation that doesn't filter out manually extracted data.
    """
    validated_response = {}

    for property_name, extracted_data in response.items():
        if property_name not in prop_table_dict:
            # Keep data even if property not in dict (manual extraction case)
            validated_response[property_name] = extracted_data
            continue

        table_entry = prop_table_dict[property_name]
        table_data = table_entry.get('table_data', []) if isinstance(table_entry, dict) else []

        if not table_data:
            validated_response[property_name] = extracted_data
            continue

        # Find the correct column for this property
        header_row = table_data[0] if table_data else {}
        matching_columns = []

        for col_key, col_header in header_row.items():
            if property_name.lower() in col_header.lower():
                matching_columns.append(col_key)

        # If no matching columns found, keep all data (manual extraction case)
        if not matching_columns:
            print(f"No matching columns found for {property_name}, keeping all extracted data")
            validated_response[property_name] = extracted_data
            continue

        # Validate extracted values against matching columns with relaxed matching
        valid_data = []
        for item in extracted_data:
            value = item.get('value', '').strip()
            compound = item.get('compound', '').strip()

            # Check if this value exists in any of the matching columns
            value_found = False
            compound_found = False

            for row in table_data[1:]:  # Skip header row
                # Check compound name in first column
                if compound and str(compound) in str(row.get('0', '')):
                    compound_found = True

                # Check value in matching columns with relaxed matching
                for col_key in matching_columns:
                    if col_key in row:
                        cell_value = str(row[col_key]).strip()
                        # Relaxed matching: check if core numbers match
                        if value and (str(value) in cell_value or
                                    any(part.strip() in cell_value for part in str(value).split('±') if part.strip())):
                            value_found = True
                            break

                if value_found and compound_found:
                    break

            # Keep data if either value or compound is found, or if it's manual extraction
            if value_found or compound_found or len(extracted_data) <= 10:  # Assume manual extraction if small dataset
                valid_data.append(item)
            else:
                print(f"Filtered out: {item} (value: {value_found}, compound: {compound_found})")

        # If no valid data found but we have extracted data, keep it (manual extraction case)
        if not valid_data and extracted_data:
            print(f"No validation matches for {property_name}, but keeping {len(extracted_data)} manually extracted items")
            valid_data = extracted_data

        validated_response[property_name] = valid_data

    return validated_response


def extract_data_to_dataframe(response, table_metadata=None):
    """
    Extract data from the response and convert it into a DataFrame with all data in string format.
    Includes metadata columns for coordinates, page_number, and page_size.

    Args:
        response (dict): The response object containing data.
        table_metadata (dict): Dictionary containing table metadata for each property.
    Returns:
        pd.DataFrame: A DataFrame with all data in string format including metadata.
    """
    # Check if the response contains data
    if not response:
        return pd.DataFrame()

    # Initialize a list to hold DataFrames
    df_list = []

    # Iterate over each key-value pair in the response
    for key, data in response.items():
        # Convert the data to a DataFrame
        df = pd.DataFrame(data)

        # Ensure all columns are in string format
        for col in df.columns:
            df[col] = df[col].astype(str)

        # Add a column to indicate the data source (key)
        df['source_key'] = key
        df['property_name'] = key

        # Add metadata columns if available
        if table_metadata and key in table_metadata:
            metadata = table_metadata[key]

            # Add table_id
            df['table_id'] = str(metadata.get('table_id', ''))

            # Add page_number
            df['page_number'] = str(metadata.get('page_number', ''))

            # Add coordinates as separate columns
            coordinates = metadata.get('coordinates', {})
            df['coord_left'] = str(coordinates.get('left', ''))
            df['coord_top'] = str(coordinates.get('top', ''))
            df['coord_right'] = str(coordinates.get('right', ''))
            df['coord_bottom'] = str(coordinates.get('bottom', ''))
            df['coord_origin'] = str(coordinates.get('coord_origin', ''))

            # Add page_size as separate columns
            page_size = metadata.get('page_size', {})
            df['page_width'] = str(page_size.get('width', ''))
            df['page_height'] = str(page_size.get('height', ''))
        else:
            # Add empty metadata columns if no metadata available
            df['table_id'] = ''
            df['page_number'] = ''
            df['coord_left'] = ''
            df['coord_top'] = ''
            df['coord_right'] = ''
            df['coord_bottom'] = ''
            df['coord_origin'] = ''
            df['page_width'] = ''
            df['page_height'] = ''

        # Append the DataFrame to the list
        df_list.append(df)

    # Concatenate all DataFrames
    result_df = pd.concat(df_list, ignore_index=True)

    return result_df



def convert_single_quotes_to_double_quotes(prop_table_dict_list):
    """
    Convert all single quotes to double quotes in the prop_table_dict_list to ensure valid JSON format.

    Args:
        prop_table_dict_list (list): List of dictionaries containing property table data with potential single quotes
    
    Returns:
        list: Same list structure but with all single quotes converted to double quotes for valid JSON
    """
    try:
        converted_list = []
    
        for prop_table_dict in prop_table_dict_list:
            converted_dict = {}
        
            for property_name, table_entry in prop_table_dict.items():
                if isinstance(table_entry, dict):
                    # Convert the entire dictionary to JSON string and back to ensure proper quoting
                    json_string = json.dumps(table_entry, ensure_ascii=False)
                    converted_entry = json.loads(json_string)
                    converted_dict[property_name] = converted_entry
                elif isinstance(table_entry, str):
                    # For string entries, convert single quotes to double quotes carefully
                    converted_dict[property_name] = convert_string_quotes(table_entry)
                else:
                    # For other types, keep as is
                    converted_dict[property_name] = table_entry
        
            converted_list.append(converted_dict)
    
        return converted_list
    
    except Exception as error:
        print(f"Error in convert_single_quotes_to_double_quotes: {str(error)}")
        return prop_table_dict_list


def convert_string_quotes(text):
    """
    Helper function to convert single quotes to double quotes in string content.
    Handles edge cases like contractions and nested quotes.

    Args:
        text (str): Input string with potential single quotes
    
    Returns:
        str: String with single quotes converted to double quotes where appropriate
    """
    try:
        # Handle contractions and possessives (don't convert these single quotes)
        # Pattern to match contractions like don't, can't, it's, etc.
        contraction_pattern = r"\b\w+'\w+\b"
        contractions = re.findall(contraction_pattern, text)
    
        # Temporarily replace contractions with placeholders
        temp_text = text
        placeholders = {}
        for i, contraction in enumerate(contractions):
            placeholder = f"__CONTRACTION_{i}__"
            placeholders[placeholder] = contraction
            temp_text = temp_text.replace(contraction, placeholder, 1)
    
        # Convert remaining single quotes to double quotes
        temp_text = temp_text.replace("'", '"')
    
        # Restore contractions
        for placeholder, contraction in placeholders.items():
            temp_text = temp_text.replace(placeholder, contraction)
    
        return temp_text
    
    except Exception as error:
        print(f"Error in convert_string_quotes: {str(error)}")
        return text


def convert_quotes_comprehensive(prop_table_dict_list):
    """
    Comprehensive function to convert single quotes to double quotes throughout the entire data structure.
    This handles nested dictionaries, lists, and string values recursively.

    Args:
        prop_table_dict_list (list): List of dictionaries containing property table data
    
    Returns:
        list: Converted list with proper double quotes for JSON compatibility
    """
    try:
        # Convert the entire structure to JSON string and back
        # This ensures all quotes are properly formatted for JSON
        json_string = json.dumps(prop_table_dict_list, ensure_ascii=False, indent=2)
        converted_list = json.loads(json_string)
    
        return converted_list
    
    except Exception as error:
        print(f"Error in convert_quotes_comprehensive: {str(error)}")
        # Fallback to manual conversion if JSON conversion fails
        return convert_single_quotes_to_double_quotes(prop_table_dict_list)


def fix_json_quotes_in_table_data(prop_table_dict_list):
    """
    Specifically designed to fix quote issues in table_data JSON structures.
    Handles the nested table_data format with proper JSON conversion.

    Args:
        prop_table_dict_list (list): List of property table dictionaries
    
    Returns:
        list: Fixed list with proper JSON formatting
    """
    try:
        fixed_list = []
    
        for prop_table_dict in prop_table_dict_list:
            fixed_dict = {}
        
            for property_name, table_entry in prop_table_dict.items():
                if isinstance(table_entry, dict):
                    fixed_entry = {}
                
                    # Handle each field in the table entry
                    for key, value in table_entry.items():
                        if key == 'table_data' and isinstance(value, list):
                            # Special handling for table_data which contains list of dictionaries
                            fixed_table_data = []
                            for row in value:
                                if isinstance(row, dict):
                                    # Convert each row dictionary properly
                                    fixed_row = {}
                                    for col_key, col_value in row.items():
                                        # Ensure string values are properly quoted
                                        if isinstance(col_value, str):
                                            fixed_row[str(col_key)] = col_value
                                        else:
                                            fixed_row[str(col_key)] = str(col_value)
                                    fixed_table_data.append(fixed_row)
                                else:
                                    fixed_table_data.append(row)
                            fixed_entry[key] = fixed_table_data
                        else:
                            # Handle other fields normally
                            fixed_entry[key] = value
                
                    fixed_dict[property_name] = fixed_entry
                else:
                    fixed_dict[property_name] = table_entry
        
            fixed_list.append(fixed_dict)
    
        # Final JSON conversion to ensure everything is properly formatted
        json_string = json.dumps(fixed_list, ensure_ascii=False)
        final_list = json.loads(json_string)
    
        return final_list
    
    except Exception as error:
        print(f"Error in fix_json_quotes_in_table_data: {str(error)}")
        return prop_table_dict_list

def simple_quote_converter(prop_table_dict_list):
    """
    Simple function to convert single quotes to double quotes using JSON serialization.

    Args:
        prop_table_dict_list (list): Input list with potential single quote issues
    
    Returns:
        list: List with proper double quotes
    """
    try:
        # Convert to JSON string (this automatically uses double quotes)
        json_str = json.dumps(prop_table_dict_list, ensure_ascii=False)
    
        # Convert back to Python object (now with proper quoting)
        converted_list = json.loads(json_str)
    
        return converted_list
    
    except Exception as error:
        print(f"Error in simple_quote_converter: {str(error)}")
        return prop_table_dict_list


def get_prop_data_by_llama3_model(tan_number, prop_table_dict_list, output_file):
    """
    Extract property data with model-specific handling.
    """
    # Fix input data quotes (this works well with LLaMA 3.2)
    # url = "http://172.27.7.85:5007/lamma_3_1_model"  # or lamma_3_2_model or mistral_model
    url= "http://172.27.7.85:5007/lamma_3_2_model"
    # url = "http://172.27.7.85:5007/mistral_model"
    if "lamma_3_2_model" in url:
        prop_table_dict_list = simple_quote_converter(prop_table_dict_list)
        print("Applied quote conversion for LLaMA 3.2...")

    # Convert coordinates
    converted_prop_table_dict_list = convert_table_coordinates_in_list(prop_table_dict_list)
    print("Converted coordinates for FITZ compatibility...")

    # Initialize an empty list to store the dataframes
    tan_df_list = []

    # Iterate over each property table dictionary (now with converted coordinates)
    for prop_table_dict in converted_prop_table_dict_list:
        # Extract metadata for each property
        table_metadata = {}
        for property_name, table_entry in prop_table_dict.items():
            if isinstance(table_entry, dict):
                table_metadata[property_name] = table_entry

        # Get the response from the LLaMA3 model
        response = get_response(url, prop_table_dict)
        print(f"Raw response keys: {list(response.keys())}")
        for key, data in response.items():
            print(f"  {key}: {len(data)} items extracted")
            if data:
                print(f"    Sample: {data[0]}")

        validated_response = validate_extracted_data(response, prop_table_dict)  # Add validation
        print(f"Validated response keys: {list(validated_response.keys())}")
        for key, data in validated_response.items():
            print(f"  {key}: {len(data)} items after validation")

        df = extract_data_to_dataframe(validated_response, table_metadata)  # Use validated response
        print(f"DataFrame shape after extraction: {df.shape}")

        # Check if the data is not empty
        if not df.empty:
            tan_df_list.append(df)

    # Concatenate the dataframes
    if tan_df_list:
        tan_df = pd.concat(tan_df_list, ignore_index=True)
        tan_df['tan_name'] = tan_number
    else:
        # Return an empty dataframe if no data was extracted with all expected columns
        tan_df = pd.DataFrame(columns=[
            'property_name', 'value', 'unit', 'compound', 'source_key', 'tan_name',
            'table_id', 'page_number', 'coord_left', 'coord_top', 'coord_right',
            'coord_bottom', 'coord_origin', 'page_width', 'page_height'
        ])

    # Convert all columns to string explicitly (if needed)
    tan_df = tan_df.astype(str)

    # Save to Excel while preserving data types as strings
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        tan_df.to_excel(writer, index=False)
    # Return the dataframe
    tan_df.drop_duplicates(inplace=True)
    return tan_df


def remove_duplicate_table_rows(prop_table_dict_list):
    """
    Remove duplicate rows from table data to prevent model confusion.

    Args:
        prop_table_dict_list (list): List of property table dictionaries
    
    Returns:
        list: Cleaned list with duplicate rows removed
    """
    try:
        cleaned_list = []
    
        for prop_table_dict in prop_table_dict_list:
            cleaned_dict = {}
        
            for property_name, table_entry in prop_table_dict.items():
                if isinstance(table_entry, dict):
                    cleaned_entry = table_entry.copy()
                    table_data = table_entry.get('table_data', [])
                
                    if table_data:
                        # Keep header row (first row)
                        header_row = table_data[0]
                        data_rows = table_data[1:]
                    
                        # Remove duplicate rows based on first column (compound name)
                        seen_compounds = set()
                        unique_rows = [header_row]
                    
                        for row in data_rows:
                            compound_key = row.get('0', '')  # First column
                            if compound_key not in seen_compounds:
                                seen_compounds.add(compound_key)
                                unique_rows.append(row)
                            else:
                                print(f"Removed duplicate row for compound: {compound_key}")
                    
                        cleaned_entry['table_data'] = unique_rows
                
                    cleaned_dict[property_name] = cleaned_entry
                else:
                    cleaned_dict[property_name] = table_entry
        
            cleaned_list.append(cleaned_dict)
    
        return cleaned_list
    
    except Exception as error:
        print(f"Error in remove_duplicate_table_rows: {str(error)}")
        return prop_table_dict_list


def safe_json_parse(response_text, property_name):
    """
    Enhanced JSON parsing with better error handling for LLaMA 3.2.
    """
    try:
        # Strategy 1: Direct parsing
        return json.loads(response_text)
    except json.JSONDecodeError as e:
        print(f"Direct JSON parsing failed for llama3.2: {e}")
        
        try:
            # Strategy 2: Clean response
            cleaned = clean_json_response(response_text)
            return json.loads(cleaned)
        except json.JSONDecodeError as e:
            print(f"Cleaned JSON parsing failed for llama3.2: {e}")
            
            try:
                # Strategy 3: Fix quotes and commas
                fixed = fix_json_structure(response_text)
                return json.loads(fixed)
            except json.JSONDecodeError as e:
                print(f"Quote fixing failed for llama3.2: {e}")
                
                try:
                    # Strategy 4: Manual parsing as last resort
                    return manual_json_parse(response_text, property_name)
                except Exception as e:
                    print(f"Manual parsing failed for llama3.2: {e}")
                    return []


def clean_json_response(response_text):
    """
    Enhanced cleaning for JSON responses.
    """
    import re
    
    response_text = response_text.strip()
    
    # Remove markdown code blocks
    response_text = re.sub(r'```json\s*', '', response_text, flags=re.IGNORECASE)
    response_text = re.sub(r'```\s*', '', response_text)
    
    # Remove explanatory text
    response_text = re.sub(r'^[^[\{]*', '', response_text)  # Remove text before JSON
    response_text = re.sub(r'[^}\]]*$', '', response_text)  # Remove text after JSON
    
    # Find JSON boundaries
    start_idx = response_text.find('[')
    end_idx = response_text.rfind(']')
    
    if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
        response_text = response_text[start_idx:end_idx + 1]
    
    return response_text.strip()


def fix_json_structure(response_text):
    """
    Fix common JSON structure issues in LLaMA 3.2 responses.
    """
    import re
    
    # Clean first
    response_text = clean_json_response(response_text)
    
    # Fix missing commas between objects
    response_text = re.sub(r'}\s*{', '}, {', response_text)
    
    # Fix missing commas between key-value pairs
    response_text = re.sub(r'"\s*"([^"]*)":', '", "\1":', response_text)
    
    # Fix missing commas after values
    response_text = re.sub(r'"\s*\n\s*"', '", "', response_text)
    
    # Fix spacing around colons
    response_text = re.sub(r'"\s*:\s*"', '": "', response_text)
    
    # Remove trailing commas
    response_text = re.sub(r',\s*}', '}', response_text)
    response_text = re.sub(r',\s*]', ']', response_text)
    
    # Fix newlines within JSON
    response_text = re.sub(r'\n\s*', ' ', response_text)
    
    # Fix multiple spaces
    response_text = re.sub(r'\s+', ' ', response_text)
    
    return response_text


def manual_json_parse(response_text, property_name):
    """
    Enhanced manual parsing for LLaMA 3.2 comma delimiter issues.
    """
    import re

    results = []

    try:
        print(f"Attempting manual parsing for {property_name}")
        print(f"Response text length: {len(response_text)}")

        # Strategy 1: Look for complete object patterns with flexible comma handling
        # Handle cases where commas might be missing or malformed
        object_patterns = [
            r'\{[^}]*"property_name"[^}]*"value"[^}]*"unit"[^}]*"compound"[^}]*\}',
            r'\{[^}]*property_name[^}]*value[^}]*unit[^}]*compound[^}]*\}',
            r'"property_name"[^"]*"([^"]*)"[^"]*"value"[^"]*"([^"]*)"[^"]*"unit"[^"]*"([^"]*)"[^"]*"compound"[^"]*"([^"]*)"'
        ]

        for pattern in object_patterns:
            matches = re.findall(pattern, response_text, re.DOTALL | re.IGNORECASE)
            if matches:
                print(f"Found {len(matches)} matches with pattern")
                break

        # Strategy 2: Extract individual fields more flexibly
        if not matches:
            # Look for individual field patterns
            prop_matches = re.findall(r'"?property_name"?\s*:?\s*"?([^",}]+)"?', response_text, re.IGNORECASE)
            value_matches = re.findall(r'"?value"?\s*:?\s*"?([^",}]+)"?', response_text, re.IGNORECASE)
            unit_matches = re.findall(r'"?unit"?\s*:?\s*"?([^",}]*)"?', response_text, re.IGNORECASE)
            compound_matches = re.findall(r'"?compound"?\s*:?\s*"?([^",}]+)"?', response_text, re.IGNORECASE)

            print(f"Individual matches - prop: {len(prop_matches)}, value: {len(value_matches)}, unit: {len(unit_matches)}, compound: {len(compound_matches)}")

            # Try to pair them up
            max_len = max(len(prop_matches), len(value_matches), len(compound_matches))
            for i in range(max_len):
                prop = prop_matches[i] if i < len(prop_matches) else property_name
                value = value_matches[i] if i < len(value_matches) else ""
                unit = unit_matches[i] if i < len(unit_matches) else ""
                compound = compound_matches[i] if i < len(compound_matches) else f"Unknown_{i+1}"

                if value and compound:  # Only add if we have essential data
                    results.append({
                        "property_name": prop.strip().strip('"'),
                        "value": value.strip().strip('"'),
                        "unit": unit.strip().strip('"'),
                        "compound": compound.strip().strip('"')
                    })

        # Strategy 3: Look for numerical patterns that might be values
        if not results:
            print("Trying numerical pattern extraction...")
            # Look for numerical values that could be property values
            numerical_pattern = r'([0-9]+\.?[0-9]*\s*[±]?\s*[0-9]*\.?[0-9]*)'
            compound_pattern = r'([A-Za-z][A-Za-z0-9\(\)/\-\s]*)'

            numbers = re.findall(numerical_pattern, response_text)
            compounds = re.findall(compound_pattern, response_text)

            print(f"Found {len(numbers)} numbers and {len(compounds)} potential compounds")

            # Filter compounds to reasonable length and content
            filtered_compounds = [c.strip() for c in compounds if 2 <= len(c.strip()) <= 20 and not c.strip().lower() in ['property', 'value', 'unit', 'compound']]

            for i, number in enumerate(numbers[:6]):  # Limit to 6 matches
                compound = filtered_compounds[i] if i < len(filtered_compounds) else f"Sample_{i+1}"
                results.append({
                    "property_name": property_name,
                    "value": number.strip(),
                    "unit": "",
                    "compound": compound
                })

        if results:
            print(f"Manual parsing extracted {len(results)} items for {property_name}")
            for i, result in enumerate(results):
                print(f"  {i+1}: {result}")
        else:
            print(f"Manual parsing found no data for {property_name}")

        return results

    except Exception as e:
        print(f"Manual parsing error: {e}")
        return []



def fix_json_commas(response_text):
    """
    Fix missing commas in JSON response.
    
    Args:
        response_text (str): JSON text with potential comma issues
        
    Returns:
        str: Fixed JSON string
    """
    import re
    
    # Fix missing commas between objects
    response_text = re.sub(r'}\s*{', '}, {', response_text)
    
    # Fix missing commas between key-value pairs on new lines
    response_text = re.sub(r'"\s*\n\s*"', '", "', response_text)
    
    # Fix missing commas after values before new keys
    response_text = re.sub(r'"\s*"([^"]*)":', '", "\1":', response_text)
    
    # Remove trailing commas
    response_text = re.sub(r',\s*}', '}', response_text)
    response_text = re.sub(r',\s*]', ']', response_text)
    
    return response_text




if __name__ == "__main__":
    # Get the TAN number of pdf file
    tan_number = "41484277K"

    # Get the property table dictionary list
    prop_table_dict_list = [
    {"DS": {"table_id": "table_3_1", "title": "Table 1 The DSC parameters of BA-a/AC at different AC wt% loading.", "table_data": [{"0": "Sample", "1": "Initial exotherm ( /C176 C)", "2": "Peak exotherm ( /C176 C)", "3": "Heat of reaction (J/g)", "4": "Heat of reaction (J/g)"}, {"0": "BA-a", "1": "186", "2": "234.5", "3": "390.7", "4": "390.7"}, {"0": "BA-a/AC1", "1": "179", "2": "230.4", "3": "375.8", "4": "375.8"}, {"0": "BA-a/AC2", "1": "175", "2": "229.6", "3": "363.4", "4": "363.4"}, {"0": "BA-a/AC3", "1": "172", "2": "227.5", "3": "323.9", "4": "323.9"}, {"0": "BA-a/AC4", "1": "167", "2": "224.6", "3": "302.5", "4": "302.5"}, {"0": "BA-a/AC5", "1": "164", "2": "224.1", "3": "322.5", "4": "322.5"}, {"0": "BA-a/AC5", "1": "164", "2": "224.1", "3": "322.5", "4": "322.5"}], "page_number": 3, "coordinates": {"left": 32.093475341796875, "top": 449.9383239746094, "right": 284.178466796875, "bottom": 371.1972351074219, "coord_origin": "BOTTOMLEFT"}, "page_size": {"width": 595.2760009765625, "height": 793.7009887695312}}},
    
    {"tensile stress": {"table_id": "table_4_2", "title": "Tensile, impact and brittleness parameters for pristine poly(BA-a) and AC filled composites.", "table_data": [{"0": "Sample", "1": "Tensile Stress", "2": "(MPa)", "3": "Young modulus (GPa)", "4": "Impact strength (kJ/m 2 )", "5": "Brittleness ( B ) (% Pa/10 10 )", "6": "Brittleness ( B ) (% Pa/10 10 )"}, {"0": "P(BA-a)", "1": "27.93 ± 0.78", "2": "27.93 ± 0.78", "3": "2.16 ± 0.122", "4": "1.4 ± 0.12", "5": "3.26", "6": "3.26"}, {"0": "P(BA-a/AC1)", "1": "32.49 ± 1.21", "2": "32.49 ± 1.21", "3": "2.56 ± 0.191", "4": "2.7 ± 0.12", "5": "2.74", "6": "2.74"}, {"0": "P(BA-a/AC2)", "1": "39.55 ± 1.53", "2": "39.55 ± 1.53", "3": "2.84 ± 0.182", "4": "3.9 ± 0.14", "5": "2.50", "6": "2.50"}, {"0": "P(BA-a/AC3)", "1": "43.03 ± 1.12", "2": "43.03 ± 1.12", "3": "3.10 ± 0.175", "4": "4.7 ± 0.22", "5": "2.35", "6": "2.35"}, {"0": "P(BA-a/AC4)", "1": "44.84 ± 1.31", "2": "44.84 ± 1.31", "3": "3.31 ± 0.205", "4": "5.7 ± 0.21", "5": "2.15", "6": "2.15"}, {"0": "P(BA-a/AC5)", "1": "48.82 ± 1.19", "2": "48.82 ± 1.19", "3": "3.54 ± 0.174", "4": "5.2 ± 0.16", "5": "2.12", "6": "2.12"}], "page_number": 4, "coordinates": {"left": 42.0387077331543, "top": 707.4191436767578, "right": 562.8633422851562, "bottom": 637.2298889160156, "coord_origin": "BOTTOMLEFT"}, "page_size": {"width": 595.2760009765625, "height": 793.7009887695312}}},
    
    {"Young modulus": {"table_id": "table_4_2", "title": "Tensile, impact and brittleness parameters for pristine poly(BA-a) and AC filled composites.", "table_data": [{"0": "Sample", "1": "Tensile Stress", "2": "(MPa)", "3": "Young modulus (GPa)", "4": "Impact strength (kJ/m 2 )", "5": "Brittleness ( B ) (% Pa/10 10 )", "6": "Brittleness ( B ) (% Pa/10 10 )"}, {"0": "P(BA-a)", "1": "27.93 ± 0.78", "2": "27.93 ± 0.78", "3": "2.16 ± 0.122", "4": "1.4 ± 0.12", "5": "3.26", "6": "3.26"}, {"0": "P(BA-a/AC1)", "1": "32.49 ± 1.21", "2": "32.49 ± 1.21", "3": "2.56 ± 0.191", "4": "2.7 ± 0.12", "5": "2.74", "6": "2.74"}, {"0": "P(BA-a/AC2)", "1": "39.55 ± 1.53", "2": "39.55 ± 1.53", "3": "2.84 ± 0.182", "4": "3.9 ± 0.14", "5": "2.50", "6": "2.50"}, {"0": "P(BA-a/AC3)", "1": "43.03 ± 1.12", "2": "43.03 ± 1.12", "3": "3.10 ± 0.175", "4": "4.7 ± 0.22", "5": "2.35", "6": "2.35"}, {"0": "P(BA-a/AC4)", "1": "44.84 ± 1.31", "2": "44.84 ± 1.31", "3": "3.31 ± 0.205", "4": "5.7 ± 0.21", "5": "2.15", "6": "2.15"}, {"0": "P(BA-a/AC5)", "1": "48.82 ± 1.19", "2": "48.82 ± 1.19", "3": "3.54 ± 0.174", "4": "5.2 ± 0.16", "5": "2.12", "6": "2.12"}], "page_number": 4, "coordinates": {"left": 42.0387077331543, "top": 707.4191436767578, "right": 562.8633422851562, "bottom": 637.2298889160156, "coord_origin": "BOTTOMLEFT"}, "page_size": {"width": 595.2760009765625, "height": 793.7009887695312}}}
]


    output_file =f"{tan_number}_prop_data.xlsx"

    # Get the property data from the LLaMA3 model using the TAN number and property table dictionary list
    prop_df = get_prop_data_by_llama3_model(tan_number, prop_table_dict_list, output_file)

    # Save the property data to an Excel file
    prop_df.to_excel(output_file, index=False)
    print("---------------------------")
    print(prop_df.to_markdown())
    print(prop_df.shape)
####################################################################
