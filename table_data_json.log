2025-06-06 13:11:58,059 - ERROR: Error in extract_tables_with_properties: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2988, in __init__
    doc = mupdf.fz_open_document(filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\mupdf.py", line 45162, in fz_open_document
    return _mupdf.fz_open_document(filename)
pymupdf.mupdf.FzErrorUnsupported: code=6: cannot find document handler for file: extracted_data_folder\41405412A.article.003\table_data.json

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Anand\physical_property_mac_data_validation\get_table_calculated_dictionary.py", line 67, in extract_table_sentences
    pdf_document = fitz.open(pdf_path)  # type: ignore
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2991, in __init__
    raise FileDataError(f'Failed to open file {filename!r}.') from e
pymupdf.FileDataError: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
2025-06-06 13:11:58,088 - ERROR: Error in extract_tables_with_properties: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2988, in __init__
    doc = mupdf.fz_open_document(filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\mupdf.py", line 45162, in fz_open_document
    return _mupdf.fz_open_document(filename)
pymupdf.mupdf.FzErrorUnsupported: code=6: cannot find document handler for file: extracted_data_folder\41405412A.article.003\table_data.json

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Anand\physical_property_mac_data_validation\get_table_calculated_dictionary.py", line 67, in extract_table_sentences
    pdf_document = fitz.open(pdf_path)  # type: ignore
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2991, in __init__
    raise FileDataError(f'Failed to open file {filename!r}.') from e
pymupdf.FileDataError: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
