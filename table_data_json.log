2025-06-06 13:11:58,059 - ERROR: Error in extract_tables_with_properties: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2988, in __init__
    doc = mupdf.fz_open_document(filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\mupdf.py", line 45162, in fz_open_document
    return _mupdf.fz_open_document(filename)
pymupdf.mupdf.FzErrorUnsupported: code=6: cannot find document handler for file: extracted_data_folder\41405412A.article.003\table_data.json

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Anand\physical_property_mac_data_validation\get_table_calculated_dictionary.py", line 67, in extract_table_sentences
    pdf_document = fitz.open(pdf_path)  # type: ignore
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2991, in __init__
    raise FileDataError(f'Failed to open file {filename!r}.') from e
pymupdf.FileDataError: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
2025-06-06 13:11:58,088 - ERROR: Error in extract_tables_with_properties: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2988, in __init__
    doc = mupdf.fz_open_document(filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\mupdf.py", line 45162, in fz_open_document
    return _mupdf.fz_open_document(filename)
pymupdf.mupdf.FzErrorUnsupported: code=6: cannot find document handler for file: extracted_data_folder\41405412A.article.003\table_data.json

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Anand\physical_property_mac_data_validation\get_table_calculated_dictionary.py", line 67, in extract_table_sentences
    pdf_document = fitz.open(pdf_path)  # type: ignore
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2991, in __init__
    raise FileDataError(f'Failed to open file {filename!r}.') from e
pymupdf.FileDataError: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
2025-06-06 13:18:05,447 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:18:05,557 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:18:05,603 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:18:05,638 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:18:05,908 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:18:06,026 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:18:06,065 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:18:06,103 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:18:06,230 - ERROR: Error in extract_tables_with_properties: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2988, in __init__
    doc = mupdf.fz_open_document(filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\mupdf.py", line 45162, in fz_open_document
    return _mupdf.fz_open_document(filename)
pymupdf.mupdf.FzErrorUnsupported: code=6: cannot find document handler for file: extracted_data_folder\41405412A.article.003\table_data.json

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Anand\physical_property_mac_data_validation\get_table_calculated_dictionary.py", line 67, in extract_table_sentences
    pdf_document = fitz.open(pdf_path)  # type: ignore
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2991, in __init__
    raise FileDataError(f'Failed to open file {filename!r}.') from e
pymupdf.FileDataError: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
2025-06-06 13:18:06,261 - ERROR: Error in extract_tables_with_properties: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2988, in __init__
    doc = mupdf.fz_open_document(filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\mupdf.py", line 45162, in fz_open_document
    return _mupdf.fz_open_document(filename)
pymupdf.mupdf.FzErrorUnsupported: code=6: cannot find document handler for file: extracted_data_folder\41405412A.article.003\table_data.json

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Anand\physical_property_mac_data_validation\get_table_calculated_dictionary.py", line 67, in extract_table_sentences
    pdf_document = fitz.open(pdf_path)  # type: ignore
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2991, in __init__
    raise FileDataError(f'Failed to open file {filename!r}.') from e
pymupdf.FileDataError: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
2025-06-06 13:18:43,338 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:18:43,464 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:18:43,510 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:18:43,542 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:18:43,839 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:18:43,964 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:18:44,011 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:18:44,042 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:18:44,167 - ERROR: Error in extract_tables_with_properties: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2988, in __init__
    doc = mupdf.fz_open_document(filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\mupdf.py", line 45162, in fz_open_document
    return _mupdf.fz_open_document(filename)
pymupdf.mupdf.FzErrorUnsupported: code=6: cannot find document handler for file: extracted_data_folder\41405412A.article.003\table_data.json

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Anand\physical_property_mac_data_validation\get_table_calculated_dictionary.py", line 67, in extract_table_sentences
    pdf_document = fitz.open(pdf_path)  # type: ignore
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2991, in __init__
    raise FileDataError(f'Failed to open file {filename!r}.') from e
pymupdf.FileDataError: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
2025-06-06 13:18:44,198 - ERROR: Error in extract_tables_with_properties: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2988, in __init__
    doc = mupdf.fz_open_document(filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\mupdf.py", line 45162, in fz_open_document
    return _mupdf.fz_open_document(filename)
pymupdf.mupdf.FzErrorUnsupported: code=6: cannot find document handler for file: extracted_data_folder\41405412A.article.003\table_data.json

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Anand\physical_property_mac_data_validation\get_table_calculated_dictionary.py", line 67, in extract_table_sentences
    pdf_document = fitz.open(pdf_path)  # type: ignore
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2991, in __init__
    raise FileDataError(f'Failed to open file {filename!r}.') from e
pymupdf.FileDataError: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
2025-06-06 13:19:29,012 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:19:29,152 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:19:29,199 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:19:29,229 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:19:29,465 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:19:29,606 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:19:29,637 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:19:29,686 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:19:29,817 - ERROR: Error in extract_tables_with_properties: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2988, in __init__
    doc = mupdf.fz_open_document(filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\mupdf.py", line 45162, in fz_open_document
    return _mupdf.fz_open_document(filename)
pymupdf.mupdf.FzErrorUnsupported: code=6: cannot find document handler for file: extracted_data_folder\41405412A.article.003\table_data.json

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Anand\physical_property_mac_data_validation\get_table_calculated_dictionary.py", line 67, in extract_table_sentences
    pdf_document = fitz.open(pdf_path)  # type: ignore
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2991, in __init__
    raise FileDataError(f'Failed to open file {filename!r}.') from e
pymupdf.FileDataError: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
2025-06-06 13:19:29,844 - ERROR: Error in extract_tables_with_properties: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2988, in __init__
    doc = mupdf.fz_open_document(filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\mupdf.py", line 45162, in fz_open_document
    return _mupdf.fz_open_document(filename)
pymupdf.mupdf.FzErrorUnsupported: code=6: cannot find document handler for file: extracted_data_folder\41405412A.article.003\table_data.json

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Anand\physical_property_mac_data_validation\get_table_calculated_dictionary.py", line 67, in extract_table_sentences
    pdf_document = fitz.open(pdf_path)  # type: ignore
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2991, in __init__
    raise FileDataError(f'Failed to open file {filename!r}.') from e
pymupdf.FileDataError: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
2025-06-06 13:20:06,748 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:20:06,888 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:20:06,932 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:20:06,979 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:20:07,230 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:20:07,372 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:20:07,422 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:20:07,466 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:20:07,617 - ERROR: Error in extract_tables_with_properties: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2988, in __init__
    doc = mupdf.fz_open_document(filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\mupdf.py", line 45162, in fz_open_document
    return _mupdf.fz_open_document(filename)
pymupdf.mupdf.FzErrorUnsupported: code=6: cannot find document handler for file: extracted_data_folder\41405412A.article.003\table_data.json

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Anand\physical_property_mac_data_validation\get_table_calculated_dictionary.py", line 67, in extract_table_sentences
    pdf_document = fitz.open(pdf_path)  # type: ignore
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2991, in __init__
    raise FileDataError(f'Failed to open file {filename!r}.') from e
pymupdf.FileDataError: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
2025-06-06 13:20:07,653 - ERROR: Error in extract_tables_with_properties: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2988, in __init__
    doc = mupdf.fz_open_document(filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\mupdf.py", line 45162, in fz_open_document
    return _mupdf.fz_open_document(filename)
pymupdf.mupdf.FzErrorUnsupported: code=6: cannot find document handler for file: extracted_data_folder\41405412A.article.003\table_data.json

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Anand\physical_property_mac_data_validation\get_table_calculated_dictionary.py", line 67, in extract_table_sentences
    pdf_document = fitz.open(pdf_path)  # type: ignore
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2991, in __init__
    raise FileDataError(f'Failed to open file {filename!r}.') from e
pymupdf.FileDataError: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
2025-06-06 13:20:40,809 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:20:40,937 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:20:40,980 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:20:41,026 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:20:41,262 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:20:41,407 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:20:41,449 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:20:41,493 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:20:41,625 - ERROR: Error in extract_tables_with_properties: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2988, in __init__
    doc = mupdf.fz_open_document(filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\mupdf.py", line 45162, in fz_open_document
    return _mupdf.fz_open_document(filename)
pymupdf.mupdf.FzErrorUnsupported: code=6: cannot find document handler for file: extracted_data_folder\41405412A.article.003\table_data.json

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Anand\physical_property_mac_data_validation\get_table_calculated_dictionary.py", line 67, in extract_table_sentences
    pdf_document = fitz.open(pdf_path)  # type: ignore
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2991, in __init__
    raise FileDataError(f'Failed to open file {filename!r}.') from e
pymupdf.FileDataError: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
2025-06-06 13:20:41,652 - ERROR: Error in extract_tables_with_properties: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2988, in __init__
    doc = mupdf.fz_open_document(filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\mupdf.py", line 45162, in fz_open_document
    return _mupdf.fz_open_document(filename)
pymupdf.mupdf.FzErrorUnsupported: code=6: cannot find document handler for file: extracted_data_folder\41405412A.article.003\table_data.json

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Anand\physical_property_mac_data_validation\get_table_calculated_dictionary.py", line 67, in extract_table_sentences
    pdf_document = fitz.open(pdf_path)  # type: ignore
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2991, in __init__
    raise FileDataError(f'Failed to open file {filename!r}.') from e
pymupdf.FileDataError: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
2025-06-06 13:21:22,198 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:21:22,349 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:21:22,405 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:21:22,453 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:21:22,715 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:21:22,850 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:21:22,905 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:21:22,937 - ERROR: filter_by_prop_units Error: 'dict' object has no attribute 'replace'
2025-06-06 13:21:23,084 - ERROR: Error in extract_tables_with_properties: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2988, in __init__
    doc = mupdf.fz_open_document(filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\mupdf.py", line 45162, in fz_open_document
    return _mupdf.fz_open_document(filename)
pymupdf.mupdf.FzErrorUnsupported: code=6: cannot find document handler for file: extracted_data_folder\41405412A.article.003\table_data.json

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Anand\physical_property_mac_data_validation\get_table_calculated_dictionary.py", line 67, in extract_table_sentences
    pdf_document = fitz.open(pdf_path)  # type: ignore
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2991, in __init__
    raise FileDataError(f'Failed to open file {filename!r}.') from e
pymupdf.FileDataError: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
2025-06-06 13:21:23,109 - ERROR: Error in extract_tables_with_properties: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2988, in __init__
    doc = mupdf.fz_open_document(filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\mupdf.py", line 45162, in fz_open_document
    return _mupdf.fz_open_document(filename)
pymupdf.mupdf.FzErrorUnsupported: code=6: cannot find document handler for file: extracted_data_folder\41405412A.article.003\table_data.json

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Anand\physical_property_mac_data_validation\get_table_calculated_dictionary.py", line 67, in extract_table_sentences
    pdf_document = fitz.open(pdf_path)  # type: ignore
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2991, in __init__
    raise FileDataError(f'Failed to open file {filename!r}.') from e
pymupdf.FileDataError: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
2025-06-06 13:22:22,121 - ERROR: Error in extract_tables_with_properties: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2988, in __init__
    doc = mupdf.fz_open_document(filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\mupdf.py", line 45162, in fz_open_document
    return _mupdf.fz_open_document(filename)
pymupdf.mupdf.FzErrorUnsupported: code=6: cannot find document handler for file: extracted_data_folder\41405412A.article.003\table_data.json

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Anand\physical_property_mac_data_validation\get_table_calculated_dictionary.py", line 67, in extract_table_sentences
    pdf_document = fitz.open(pdf_path)  # type: ignore
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2991, in __init__
    raise FileDataError(f'Failed to open file {filename!r}.') from e
pymupdf.FileDataError: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
2025-06-06 13:22:22,167 - ERROR: Error in extract_tables_with_properties: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2988, in __init__
    doc = mupdf.fz_open_document(filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\mupdf.py", line 45162, in fz_open_document
    return _mupdf.fz_open_document(filename)
pymupdf.mupdf.FzErrorUnsupported: code=6: cannot find document handler for file: extracted_data_folder\41405412A.article.003\table_data.json

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Anand\physical_property_mac_data_validation\get_table_calculated_dictionary.py", line 67, in extract_table_sentences
    pdf_document = fitz.open(pdf_path)  # type: ignore
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2991, in __init__
    raise FileDataError(f'Failed to open file {filename!r}.') from e
pymupdf.FileDataError: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
2025-06-06 13:29:18,838 - ERROR: Error in extract_tables_with_properties: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2988, in __init__
    doc = mupdf.fz_open_document(filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\mupdf.py", line 45162, in fz_open_document
    return _mupdf.fz_open_document(filename)
pymupdf.mupdf.FzErrorUnsupported: code=6: cannot find document handler for file: extracted_data_folder\41405412A.article.003\table_data.json

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Anand\physical_property_mac_data_validation\get_table_calculated_dictionary.py", line 67, in extract_table_sentences
    pdf_document = fitz.open(pdf_path)  # type: ignore
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2991, in __init__
    raise FileDataError(f'Failed to open file {filename!r}.') from e
pymupdf.FileDataError: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
2025-06-06 13:29:19,314 - ERROR: Error in extract_tables_with_properties: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2988, in __init__
    doc = mupdf.fz_open_document(filename)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\mupdf.py", line 45162, in fz_open_document
    return _mupdf.fz_open_document(filename)
pymupdf.mupdf.FzErrorUnsupported: code=6: cannot find document handler for file: extracted_data_folder\41405412A.article.003\table_data.json

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Anand\physical_property_mac_data_validation\get_table_calculated_dictionary.py", line 67, in extract_table_sentences
    pdf_document = fitz.open(pdf_path)  # type: ignore
  File "C:\Users\<USER>\AppData\Roaming\Python\Python39\site-packages\pymupdf\__init__.py", line 2991, in __init__
    raise FileDataError(f'Failed to open file {filename!r}.') from e
pymupdf.FileDataError: Failed to open file 'extracted_data_folder\\41405412A.article.003\\table_data.json'.
