"""
This script extracts table data from table_data.json in string format.
checks this data for any physical property present in it as given in property_excel_file.
if found returns as a dictionary and in turn for a json file returns as a list of dictionaries.
If required can use this list to convert into a dataframe with adding new columns
with standard property name mentioned in input property_synonym_file.

1.Function(extract_tables_with_properties) updated :- We are getting some noisy data and
 missing some data, which is now rectified.
2.Function(get_valid_pKa_pKb_data) added :- Property 'pKa' and 'pKb' are filtered out based on keywords.
3.Functionality added to get ignored table data with property as a ignored_dictionary.
4.Functionality added to solve issue of newline characters("\n") found in between every two words.
5.Functionalty added to check full forms of abbrevations found in pdf file.
6.Functionality added to ignore table for 'density' related synonyms ,\
      which contains 'crystallographic data'.
7.Property related unit_list is updated and taken in new excel sheet.
8.Property "pH" is removed from property and unit data.(client requirement.)
9.Updated to use table_data.json instead of PDF extraction.

Date 06-06-2025
Updated: [Current Date]
@author: Anand J<PERSON>hav
"""


## Import libraries
import os
import re
import time
import math
import pandas as pd
import logging
import json

# from nltk.tokenize import sent_tokenize
from get_table_calculated_dictionary import check_calculated_table_in_dict_values



def read_table_data_json(json_path):
    """
    Read table data from JSON file and return structured table data.

    Args:
        json_path (str): Path to the table_data.json file.

    Returns:
        list: List of dictionaries containing table information with JSON structure preserved.
    """
    try:
        with open(json_path, 'r', encoding='utf-8') as file:
            data = json.load(file)

        table_data_list = []

        if 'tables' in data:
            for table_id, table_info in data['tables'].items():
                # Preserve the complete table structure
                table_entry = {
                    'table_id': table_id,
                    'title': table_info.get('title', ''),
                    'table_data': table_info.get('table_data', []),
                    'page_number': table_info.get('page_number', ''),
                    'coordinates': table_info.get('coordinates', {}),
                    'page_size': table_info.get('page_size', {})
                }
                table_data_list.append(table_entry)

        return table_data_list

    except Exception as error:
        logging.exception(f"read_table_data_json Error: `{json_path}` file not readable : {str(error)}")
        return []



def extract_tables_with_properties_from_json(json_path, property_excel_file, abbreviation_full_form_excel):
    """
    Extract tables from a JSON file and check for the presence of properties in each table.

    Args:
        json_path (str): Path to the table_data.json file.
        property_excel_file (str): Excel file containing properties to check for in each table.
        abbreviation_full_form_excel (str): Excel file containing abbreviations and their full forms.

    Returns:
        tuple: A tuple containing two lists:
            - result_list (list): List of dictionaries with properties found in tables.
            - ignored_list (list): List of dictionaries with properties that were ignored.
    """
    try:
        result_list = []
        ignored_list = []

        # Read table data from JSON
        table_data_list = read_table_data_json(json_path)

        # Process each table
        for table_entry in table_data_list:
            if table_entry and 'table_data' in table_entry:
                # Check for properties in the table using JSON structure
                table_dict_list, table_ignored_dict_list = check_properties_in_table_json(
                    table_entry, property_excel_file
                )

                result_list.extend(table_dict_list)
                ignored_list.extend(table_ignored_dict_list)

        return result_list, ignored_list

    except Exception as error:
        logging.exception(f"extract_tables_with_properties_from_json Error: `{json_path}` file not readable : {str(error)}")
        return [], []



def check_properties_in_table_json(table_entry, property_excel_file):
    """
    Check for properties in table data by matching them with properties listed in the Excel file.
    This version works with JSON table structure.

    Args:
        table_entry (dict): Dictionary containing table information with JSON structure.
        property_excel_file (str): Path to the Excel file containing properties.

    Returns:
        tuple: A tuple containing two lists:
            - table_dict_list (list): List of dictionaries containing properties found in the table data.
            - table_ignored_dict_list (list): List of dictionaries containing properties that were ignored.
    """
    try:
        table_dict_list = []
        table_ignored_dict_list = []

        # Read the property names from the Excel file
        df_excel = pd.read_excel(property_excel_file)
        property_list = df_excel["property_name"].astype(str).tolist()

        # Debug: Print first few properties being searched
        print(f"Debug: Searching for {len(property_list)} properties")
        print(f"Debug: First 10 properties: {property_list[:10]}")

        # Check for some expected properties
        expected_props = ['heat of reaction', 'tensile stress', 'young modulus', 'impact strength', 'exotherm']
        found_props = [prop for prop in property_list if any(exp.lower() in prop.lower() for exp in expected_props)]
        print(f"Debug: Found expected properties: {found_props[:5]}")

        # Convert table data to searchable text for property detection
        title = table_entry.get('title', '')
        table_data_rows = table_entry.get('table_data', [])

        # Create searchable text from table data
        searchable_text_parts = [title]
        for row in table_data_rows:
            row_values = []
            for key in sorted(row.keys(), key=lambda x: int(x) if x.isdigit() else float('inf')):
                row_values.append(str(row[key]))
            searchable_text_parts.append(' '.join(row_values))

        searchable_text = ' '.join(searchable_text_parts)

        # Debug: Print searchable text for first table
        if table_entry.get('table_id') == 'table_3_1':
            print(f"Debug: Searchable text for first table: {searchable_text[:200]}...")

        # Iterate over each property in the property_list
        for prop in property_list:
            if len(searchable_text) != 0:
                if len(prop) < 4:
                    # Clean the searchable text
                    cleaned_text = searchable_text.replace("-\n","").replace("\n"," ").replace("fﬁ", "ffi").replace("ﬂ","fl").\
                    replace("ﬀ","ff").replace("ﬃ","ffi").replace("ﬄ","ffl").replace("ʩ","fn")\
                    .replace("æ","ae").replace("&","et").replace("�", " ").replace("#"," ")\
                    .replace("C", "°C").replace("�C", "°C").replace("°С","°C" ).replace("   "," ").replace("  "," ")

                    if re.search(fr'(?<![a-zA-Z0-9:])\b{re.escape(prop)}(?![a-zA-Z])', cleaned_text, re.IGNORECASE):
                        table_dict_list.append({prop: table_entry})

                    if any(substring in cleaned_text.lower() for substring in ["[α]d","[α]d20","[a]d","[a]d20"]):
                        table_dict_list.append({"[a]D": table_entry})

                else:
                    # Clean the searchable text
                    cleaned_text = searchable_text.replace("-\n","").replace("\n"," ").replace("fﬁ", "ffi").replace("ﬂ","fl").\
                    replace("ﬀ","ff").replace("ﬃ","ffi").replace("ﬄ","ffl").replace("ʩ","fn")\
                    .replace("æ","ae").replace("&","et").replace("�", " ").replace("#"," ")\
                    .replace("C", "°C").replace("�C", "°C").replace("°С","°C" ).replace("   "," ").replace("  "," ")

                    # Search for each variation in cleaned_text
                    if prop.lower() in cleaned_text.lower():
                        # Debug: Print when we find a potential match
                        if prop.lower() in ['tensile stress', 'young modulus']:
                            print(f"Debug: Found potential match for '{prop}' in table {table_entry.get('table_id')}")

                        variations = [prop, prop + r' ', prop + r'\d{,2} ',\
                                       prop + r'[a-zA-Z]{,2} ', prop + r'[a-zA-Z0-9]{,2} ']
                        for variation in variations:
                            if re.search(variation, cleaned_text, re.IGNORECASE):
                                if prop.lower() in ['tensile stress', 'young modulus']:
                                    print(f"Debug: Regex match confirmed for '{prop}' with variation '{variation}'")
                                table_dict_list.append({prop: table_entry})
                                break

        # Apply ignore_dictionary_for_property function for property to the collected dictionaries
        filtered_table_dict_list = []
        for table_dict in table_dict_list:
            # Debug: Print what's being filtered
            prop_name = list(table_dict.keys())[0] if table_dict else "unknown"
            print(f"Debug: Filtering property '{prop_name}' using JSON-compatible filtering")

            # Use JSON-compatible filtering functions
            filtered_dict, ignored_dict = ignore_dictionary_for_property_json(table_dict, property_excel_file)
            if filtered_dict:
                print(f"Debug: Property '{prop_name}' passed filtering")
                filtered_table_dict_list.append(filtered_dict)
            if ignored_dict:
                print(f"Debug: Property '{prop_name}' was ignored")
                table_ignored_dict_list.append(ignored_dict)

        return filtered_table_dict_list, table_ignored_dict_list

    except Exception as error:
        logging.exception(f"check_properties_in_table_json Error: {str(error)}")
        return [], []



def filter_by_keywords_json(table_dict):
    """
    Filter a dictionary based on specific keywords and ignore lists.
    JSON-compatible version that works with table entries containing JSON structure.

    Args:
    - table_dict (dict): Dictionary containing property as key and JSON table entry as value.

    Returns:
    - dict: Filtered dictionary excluding specified keywords.
    """
    try:
        # Define ignore lists (same as original function)
        yield_ignore_list = ["yield", "yielding", "yields", "yielded"]
        conc_ignore_list = ["concentration", "concentrations", "concentrated", "concentrating"]

        filtered_dict = {}
        ignored_dict = {}

        for key, table_entry in table_dict.items():
            ignore = False

            # Convert JSON table entry to searchable text
            title = table_entry.get('title', '')
            table_data_rows = table_entry.get('table_data', [])

            # Create searchable text from table data
            searchable_text_parts = [title]
            for row in table_data_rows:
                row_values = []
                for col_key in sorted(row.keys(), key=lambda x: int(x) if x.isdigit() else float('inf')):
                    row_values.append(str(row[col_key]))
                searchable_text_parts.append(' '.join(row_values))

            value = ' '.join(searchable_text_parts)

            # Apply the same filtering logic as original function
            if key.lower() in ["yield", "y", "yield strength", "yield stress"]:
                for ignore_item in yield_ignore_list:
                    pattern = re.compile(r'\b{}\b'.format(re.escape(ignore_item)), re.IGNORECASE)
                    value_clean = value.replace("-\n", "").replace("\n", " ").replace("\\n", " ").replace("  ", " ").lower()

                    if re.search(pattern, value_clean):
                        ignore = True
                        break

            elif key.lower() in ["concentration", "x", "χ", "χ1", "solubility", "solubilities", "χe", "xe"]:
                for ignore_item in conc_ignore_list:
                    pattern = re.compile(r'\b{}\b'.format(re.escape(ignore_item)), re.IGNORECASE)

                    if re.search(pattern, value):
                        ignore = True
                        break

            # Add to appropriate dictionary
            if not ignore:
                filtered_dict[key] = table_entry
            else:
                ignored_dict[key] = table_entry

        return filtered_dict, ignored_dict

    except Exception as error:
        logging.error("filter_by_keywords_json Error: %s", str(error))
        return {}, {}



def filter_by_prop_units_json(table_dict, df):
    """
    Filter a dictionary based on units present in the values and property units from a DataFrame.
    JSON-compatible version that works with table entries containing JSON structure.

    Args:
    - table_dict (dict): Dictionary containing property as key and JSON table entry as value.
    - df (DataFrame): DataFrame containing properties and their unit lists.

    Returns:
    - Tuple[dict, dict]: Filtered dictionary based on units present in the values,
      and an ignored dictionary for invalid key-value pairs.
    """
    try:
        df['property_name'] = df['property_name'].astype(str).str.lower()

        filtered_dict = {}
        ignored_dict = {}

        if len(table_dict) != 0:
            for key, table_entry in table_dict.items():
                prop_name = key.lower()
                added_to_filtered = False

                # Convert JSON table entry to searchable text
                title = table_entry.get('title', '')
                table_data_rows = table_entry.get('table_data', [])

                # Create searchable text from table data
                searchable_text_parts = [title]
                for row in table_data_rows:
                    row_values = []
                    for col_key in sorted(row.keys(), key=lambda x: int(x) if x.isdigit() else float('inf')):
                        row_values.append(str(row[col_key]))
                    searchable_text_parts.append(' '.join(row_values))

                table_data_string = ' '.join(searchable_text_parts)

                # Clean the text (same as original function)
                table_data_string = table_data_string.replace("ﬃ", "ffi").replace("fﬁ", "ffi").replace("ﬂ","fl")\
                        .replace("ﬀ","ff").replace("ﬃ","ffi").replace("ﬄ","ffl").replace("ʩ","fn")\
                        .replace("æ","ae").replace("&","et").replace("�", " ").replace("-\n","")\
                        .replace("\n", " ").replace("�"," ").replace("  ", " ").replace("C", "°C")\
                        .replace("\x01C", "°C").lower()

                if prop_name in table_data_string:
                    unit_list = df.loc[df['property_name'] == prop_name, 'unit_list'].values[0]

                    if isinstance(unit_list, float) and math.isnan(unit_list):
                        unit_list = ""
                        filtered_dict[key] = table_entry
                        added_to_filtered = True
                    else:
                        if isinstance(unit_list, str):
                            unit_list = [unit.strip() for unit in unit_list.split(',')]

                        for unit in sorted(unit_list, key=len, reverse=True):
                            try:
                                if len(unit) == 0:
                                    filtered_dict[key] = table_entry
                                    added_to_filtered = True
                                    break

                                unit = re.sub(r'[,.\s()\[\]{}!ÃÂ¡�?@#$^&*�•º_=+⁄/–−∙-]', '', unit)
                                table_data_string_clean = re.sub(r'[;:,.\s()\[\]{}·!ÃÂ¡�?@#$^&*�•º~_=+⁄/–−∙-]', '', table_data_string)

                                if unit.lower() in table_data_string_clean.lower():
                                    filtered_dict[key] = table_entry
                                    added_to_filtered = True
                                    break

                            except Exception as error:
                                pass

                        # If not added to filtered_dict, then add to ignored_dict
                        if not added_to_filtered:
                            ignored_dict[key] = table_entry
                else:
                    ignored_dict[key] = table_entry

        return filtered_dict, ignored_dict

    except Exception as error:
        logging.error("filter_by_prop_units_json Error: %s", str(error))
        return {}, {}



def ignore_dictionary_for_property_json(table_dict, property_excel_file):
    """
    Ignore properties in a dictionary based on specific criteria defined in the property Excel file.
    JSON-compatible version that works with table entries containing JSON structure.

    Args:
    - table_dict (dict): Dictionary containing property as key and JSON table entry as value.
    - property_excel_file (str): File path of the Excel file containing property names.

    Returns:
    - Tuple[dict, dict]: Final filtered dictionary after ignoring specific properties and ignored dictionary.
    """
    try:
        # Read the Excel file into a DataFrame
        df = pd.read_excel(property_excel_file)
        final_ignored_dict = {}

        # Convert the 'property_name' column to lowercase for case-insensitive comparison
        df['property_name'] = df['property_name'].astype(str).str.lower()

        # Filter the dictionary based on predefined keywords
        try:
            filtered_dict1, ignored_dict1 = filter_by_keywords_json(table_dict)
            if filtered_dict1 is not None and ignored_dict1 is not None:
                final_ignored_dict.update(ignored_dict1)
        except Exception as e:
            logging.error("Error in filter_by_keywords_json: %s", str(e))
            filtered_dict1, ignored_dict1 = None, None

        # Filter the dictionary further based on units present in the values
        try:
            final_dict, ignored_dict = filter_by_prop_units_json(filtered_dict1, df)
            if ignored_dict is not None:
                final_ignored_dict.update(ignored_dict)
        except Exception as e:
            logging.error("Error in filter_by_prop_units_json: %s", str(e))
            final_dict, ignored_dict = {}, {}

        # Return the final filtered dictionary and ignored dictionary
        return final_dict, final_ignored_dict

    except Exception as error:
        # Log an error if an exception occurs
        logging.error("ignore_dictionary_for_property_json Error: %s", str(error))

    # Return empty dictionaries if an error occurs
    return {}, {}



def get_property_table_dict_list_from_json(json_path, property_excel_file, abbreviation_full_form_excel):
    """
    Main function to get property table dictionary list from JSON file in the expected format.

    Args:
        json_path (str): Path to the table_data.json file.
        property_excel_file (str): Excel file containing properties to check for in each table.
        abbreviation_full_form_excel (str): Excel file containing abbreviations and their full forms.

    Returns:
        list: List of dictionaries with physical property as key and table data as string value.
              Format: [{'property_name': 'table_title + table_data_string'}, ...]
    """
    try:
        # Extract tables with properties from JSON
        table_data_list, table_ignored_data_list = extract_tables_with_properties_from_json(
            json_path, property_excel_file, abbreviation_full_form_excel
        )

        # Apply calculated table checks if needed
        max_sentence_length = 200
        final_valid_data, final_invalid_data, final_calculated_list = check_calculated_table_in_dict_values(
            json_path, max_sentence_length, table_data_list, table_ignored_data_list
        )

        return final_valid_data

    except Exception as error:
        logging.exception(f"get_property_table_dict_list_from_json Error: {str(error)}")
        return []



def extract_tables_with_properties(pdf_path, property_excel_file, max_line_length, abbreviation_full_form_excel):
    """
    Extract tables from a PDF file using PyMuPDF (fitz) .
    Check for the presence of properties in each table.

    Args:
        pdf_path (str): Path to the PDF file.
        property_excel_file (str): Excel file containing properties to check for in each table.

    Returns:
        list: List of dictionaries, each containing the properties found in a table \
              and the corresponding table string.
    """
    try:
        # table_lines = []
        start_pattern = r"^\s*(Table|TABLE|T A B L E|T a b l e)\.?\s*\d+[:\.]?\s*.*$|^\s*(Table|TABLE|T A B L E|T a b l e)\s+(\.|\w)+\s*[:\.]?\s*.*$"

        # max_line_length = 55

        # Open the PDF file using PyMuPDF
        pdf_document = fitz.open(pdf_path)  # type: ignore
        # print(f"{pdf_document =}")
        # List to store dictionaries for each table
        result_list = []

        # List to store ignored dictionaries for each table
        ignored_list = []
        page_num = 0
        # Iterate through each page in the PDF
        for page_num in range(pdf_document.page_count):

            # Extract text from the page
            page = pdf_document[page_num]

            raw_text = page.get_text("text")

            # Remove different types of hyphens or dashes from the extracted text
            page_text = re.sub(r'[–—⁃−]', '-', raw_text)

            # Split the text into lines
            lines = page_text.split("\n")

            # Compile the regex pattern
            start_regex = re.compile(start_pattern)

            # Temporary storage for lines that might belong to a table
            current_table_lines = []

            # Flag to indicate whether we are inside a table
            inside_table = False

            # Counter to track the lines after the start pattern
            count_after_start_pattern = 0

            # Iterate through each line in the text
            for n,line in enumerate(lines):
                
                if ".si." in pdf_path and"supporting information" in line.lower():
                        line = line.replace("Supporting information", "")\
                            .replace(" Table", "Table").replace(" TABLE", "TABLE")

                line = line.replace("#", " ").replace("  ", " ").replace("\x01C", "°C").replace("˝C", "°C")

                # if "Table" in line or "TABLE" in line or "T A B L E" in line or "T a b l e" in line:
                if line.startswith("Table")  or line.startswith("TABLE")  or\
                      line.startswith("T A B L E")  or line.startswith("T a b l e"):
                    new_line = lines[n].strip() + " " + lines[n+1].strip()

                    # Check if the line matches the start pattern
                    if start_regex.match(new_line):

                        # If we were already inside a table, start a new string for the new table pattern
                        if inside_table:
                            table_data = "\n".join(current_table_lines)

                            # table_data = check_consecutive_alphabetical(table_data)
                            if len(table_data)!=0:
                                table_data = table_data.replace("#", " ").replace("  ", " ")
                                table_dict_list, table_ignored_dict_list = check_properties_in_table(
                                    table_data, property_excel_file
                                )

                                result_list.extend(table_dict_list)
                                ignored_list.extend(table_ignored_dict_list)
                                current_table_lines = []

                        inside_table = True
                        current_table_lines.append(line)

                        # Reset the counter
                        count_after_start_pattern = 0

                # Check if we are inside a table
                elif inside_table:
                    count_after_start_pattern += 1
                    current_table_lines.append(line)

                    # Check if we have passed the third line after the start pattern
                    if count_after_start_pattern > 4 or start_regex.match(line):

                        # Check if the line has more than max_line_length characters
                        if len(line) > max_line_length:
                            table_data = "\n".join(current_table_lines[:-1])

                            if len(table_data)!=0:
                                table_data = table_data.replace("#", " ").replace("  ", " ")
                                table_dict_list, table_ignored_dict_list = check_properties_in_table(
                                    table_data, property_excel_file
                                )

                                result_list.extend(table_dict_list)
                                ignored_list.extend(table_ignored_dict_list)
                                current_table_lines = []
                                inside_table = False
                                count_after_start_pattern = 0


            # Check if there are any remaining lines after the last table line
            if current_table_lines:
                table_data = "\n".join(current_table_lines)

                if len(table_data)!=0:
                    table_data = table_data.replace("#", " ").replace("  ", " ")
                    table_dict_list, table_ignored_dict_list = check_properties_in_table(
                        table_data, property_excel_file
                    )

                    result_list.extend(table_dict_list)
                    ignored_list.extend(table_ignored_dict_list)
        # print(f"1>>{ignored_list =}")
        # Call the function and obtain the filtered list of dictionaries based on non_table data
        filtered_list, filtered_ignored_list =\
              remove_nontable_dict_from_table_list(result_list, ignored_list)
        ignored_list.extend(filtered_ignored_list)

        # Call the function and obtain the filtered list of dictionaries for pKa and pKb properties
        filtered_list2, filtered_ignored_list2 = get_valid_pKa_pKb_data(filtered_list)
        ignored_list.extend(filtered_ignored_list2)

        #Apply the check_consecutive_alphabetical function to each dictionary in filtered_list
        def is_valid_key_value(key, value):
            value = value.replace("-\n","").replace("\n"," ").replace("fﬁ", "ffi").replace("ﬂ","fl").\
                    replace("ﬀ","ff").replace("ﬃ","ffi").replace("ﬄ","ffl").replace("ʩ","fn")\
                    .replace("æ","ae").replace("&","et").replace("�", " ").replace("#"," ")\
                    .replace("   "," ").replace("  "," ")

            if len(key) >3:
                # print(f"{key = }")  #'Speciﬁc heat','Thermal expansion coefﬁcient' <-- With ligatures
                if key.lower() in value.lower():
                    return True
                else:
                    return False

            else:
                key_pattern = re.compile(r'\b' + re.escape(key) + r'\b', re.IGNORECASE)
                return bool(key_pattern.search(value))

        filtered_list_with_checked_text = check_consecutive_alphabetical_list(filtered_list2)

        final_filtered_list = []

        # Get a list of synonyms without full-forms for ignoring search in pdf document.
        key_without_fforms =["tg", "dhc", "pka", "s°", " nd ", "uts", "Dh", "δh", "Ds",\
                            "δfh", "[a]d20", "dfhƟ", "dH°f", "dmh", "tm", "dvapH",\
                            "δh","△hm","△h", "h","�H", "dgƒ", "δfh", "dfh", "δfhƟ", "dhf°",\
                             "δhm", "dhf","n", "cv", "[α]d20", "nd", "dHfus", "sm°", "vs",\
                             "dhm", "dhc", "δfh°m", "δvaph", "pkb", "δhf", "δhf°", "rp0.2",\
                            "dhf", "δfh°", "dhcomb", "dhc°", "cp", "dhr°,dSr°", "dhcond",\
                            "n20d", "σb", "dhsub", "δhr°,δSr°", "δh°f", "dfh°", "dfh", "δhc",\
                            "δs", "δhf", "[a]d", "dmho", "dhc", "mp", "δgƒ", "dhvap", "dfh°m",\
                            "ds", "[α]d", "dfh", "tc","tcr", "dh", "dhdep","δHvap", "ph",\
                             "pv", "[a]d", "bp", "tcc","dhpt", "dhf","ρ",]

        for dictionary in filtered_list_with_checked_text:

            for key, value in dictionary.items():
                value = value.replace("\x01C", "°C")
                if is_valid_key_value(key, value):

                    if len(key) <= 3:
                        abb_ff = find_full_form_in_pdf(pdf_path, key, abbreviation_full_form_excel)
                        if len(abb_ff) != 0:
                            final_filtered_list.append(dictionary)
                            break

                        elif key.lower() in key_without_fforms:
                            # print(f"{key =}")
                            final_filtered_list.append(dictionary)
                            break

                    elif len(key) > 3:
                        final_filtered_list.append(dictionary)

                        # Break out of the inner loop after checking the first key-value pair
                        break
                else:
                    ignored_list.append(dictionary)

        # print(f"{final_filtered_list =}")
        # print(f"{ignored_list=}")

        return final_filtered_list, ignored_list


    except Exception as error:
        logging.exception(f"extract_tables_with_properties Error:`{pdf_path}` file not readable : {str(error)}")
        return [],[]



def get_table_line(line):
    table_pattern = re.compile(r'\b(Table|TABLE|T\s+A\s+B\s+L\s+E|T\s+a\s+b\s+l\s+e)\s+(\d+[A-Za-z]?|\w+|[IVXLCDM]+)(?::|\.|)\s*.*$', re.IGNORECASE)

    if table_pattern.search(line):
        return line
    else:
        return None



def read_excel_to_dict(file_path):
    """
    Read an Excel file containing abbreviation and full form data and create a dictionary.

    Parameters:
    - file_path (str): The path to the Excel file.

    Returns:
    - dict: A dictionary where keys are abbreviations and values are lists of
      corresponding full forms.
    """
    try:
        # Read the Excel file into a DataFrame
        df = pd.read_excel(file_path)
        abbreviations_dict = {}

        # Iterate over each row in the DataFrame
        for _, row in df.iterrows():
            abbreviations = row['abbreviation'].split(',')
            full_forms = row['full_form'].split(',')

            # Iterate over each abbreviation in the row
            for abbr in abbreviations:
                abbr = abbr.strip()

                # Check if the abbreviation is not already in the dictionary
                if abbr not in abbreviations_dict:
                    abbreviations_dict[abbr] = []

                # Iterate over each full form in the row and add to the dictionary
                for form in full_forms:
                    abbreviations_dict[abbr].extend([f.strip() for f in form.split(';')])

        return abbreviations_dict

    except Exception as error:

        # Print the error message in case of an exception
        print(f"Error: {str(error)}")
    return {}



def find_full_form_in_pdf(pdf_path, abbreviation, abbreviation_full_form_excel):
    """
    Extracts sentences from a PDF containing a given abbreviation and its full form.

    Parameters:
    - pdf_path (str): Path to the PDF file.
    - abbreviation (str): Abbreviation to search for.
    - abbreviation_full_form_excel (str): Path to the Excel file containing standard
      abbreviations and full forms.

    Returns:
    - dict: A dictionary with the abbreviation as key and its full form as the corresponding value.
    """
    try:
        # Get standard abbreviations and full forms from the Excel file
        standard_full_forms = read_excel_to_dict(abbreviation_full_form_excel)

        # Open the PDF document
        pdf_document = fitz.open(pdf_path)  # type: ignore

        # Initialize a dictionary to store found abbreviations and full forms
        abbreviation_sentences = {}

        # Iterate over each page in the PDF
        for page_num in range(pdf_document.page_count):
            page = pdf_document[page_num]
            text = page.get_text()

            # Preprocess text to remove unnecessary characters and line breaks
            text = text.replace("-\n", "").replace("\n", " ").replace("  ", " ")

            # Split text into sentences
            sentences = re.split(r'\.\s+', text)

            # Iterate over each sentence in the text
            for sentence in sentences:

                # Check if the abbreviation is present in the sentence
                if re.search(rf'\b(?:{re.escape(abbreviation)})\b', sentence, re.IGNORECASE):

                    # Check if any of the full forms is also present in the sentence
                    for full_form in standard_full_forms.get(abbreviation, []):
                        if re.search(rf'\b(?:{re.escape(full_form)})\b', sentence, re.IGNORECASE):

                            # Check the position of full form in relation to abbreviation
                            abbrev_match = re.search(
                                rf'\b(?:{re.escape(abbreviation)})\b', sentence, re.IGNORECASE)

                            if abbrev_match:
                                abbrev_start = abbrev_match.start()
                            else:
                                abbrev_start = None

                            full_form_match = re.search(
                                rf'\b(?:{re.escape(full_form)})\b', sentence, re.IGNORECASE)
                            if full_form_match:
                                full_form_start = full_form_match.start()
                            else:
                                full_form_start = None

                            if abbrev_start is not None and abbrev_start < full_form_start: # type: ignore
                                total_abbreviation_end = abbrev_start + \
                                    len(abbreviation)

                                # Check the position of full form after the abbreviation
                                if full_form_start is not None and full_form_start - total_abbreviation_end < 10:

                                    abbreviation_sentences[abbreviation.lower(
                                    )] = full_form
                                    break

                            elif abbrev_start is not None and abbrev_start > full_form_start: # type: ignore

                                # Check the position of full form after the abbreviation
                                if full_form_start is not None:
                                    total_full_form_end = full_form_start + \
                                        len(full_form)

                                    if abbrev_start is not None and abbrev_start - total_full_form_end < 10:
                                        abbreviation_sentences[abbreviation.lower(
                                        )] = full_form
                                        break
        return abbreviation_sentences

    except Exception as error:
        # Log an error if an exception occurs
        logging.error("find_full_form_in_pdf Error: %s", str(error))
    return {}



def check_consecutive_alphabetical_list(list_of_dicts, start_line=20,\
                         consecutive_lines_threshold=15, min_consecutive_alphanumeric=10):
    """
    Check and truncate consecutive alphabetical and alphanumeric lines in a list of dictionaries.

    Parameters:
    - list_of_dicts (list): List of dictionaries where each dictionary contains keys and values.
    - start_line (int): The line number from which to start checking consecutive lines.
    - consecutive_lines_threshold (int): The threshold for the number of consecutive
      alphabetical lines to trigger truncation.
    - min_consecutive_alphanumeric (int): The minimum number of consecutive
      alphanumeric lines to trigger truncation.

    Returns:
    - list: List of dictionaries with truncated values based on the specified criteria.
    """
    result_list = []

    try:
        # Iterate over each dictionary in the list
        for dictionary in list_of_dicts:

            # Iterate over key-value pairs in the dictionary
            for k, v in dictionary.items():
                input_string = v
                truncated_string = ""
                consecutive_count = 0
                consecutive_alpha_numeric_count = 0
                consecutive_threshold_reached = False
                input_string = input_string.replace("\n ", "\n").replace("  ", " ")
                lines = input_string.split("\n")

                # Iterate over each line in the input string
                for n, line in enumerate(lines):
                    line = line.strip()

                    # Include the lines before start_line in the truncated string
                    if n < start_line:
                        truncated_string += line + "\n"
                        continue

                    truncated_string += line + "\n"

                    # Check if the line contains alphanumeric data
                    if re.match(r'^[a-zA-Z0-9\s]+$', line) and len(line.split()) == 1:
                        consecutive_alpha_numeric_count += 1

                        if consecutive_alpha_numeric_count >= min_consecutive_alphanumeric:
                            consecutive_threshold_reached = True
                            break

                    else:
                        # Reset count if the line doesn't contain consecutive alphanumeric data
                        consecutive_alpha_numeric_count = 0

                    # Check if the line contains alphabetical data
                    if re.match(r'^[a-zA-Z\s]+$', line):
                        consecutive_count += 1

                        if consecutive_count >= consecutive_lines_threshold:
                            # Exclude the last 10 consecutive alphabetical lines from the truncated string
                            truncated_string = "\n".join(lines[start_line:n - consecutive_lines_threshold])
                            consecutive_threshold_reached = True
                            break
                    else:
                        # Reset count if the line doesn't contain consecutive alphabetical data
                        consecutive_count = 0

                # If the loop completes without reaching the threshold, include the entire string
                if not consecutive_threshold_reached:
                    truncated_string = input_string

                # Update the dictionary with the truncated string
                dictionary[k] = truncated_string.strip()

                # Append the updated dictionary to the result list
                result_list.append(dictionary)

    except Exception as error:
        ## Log an error if an exception occurs
        logging.error("check_consecutive_alphabetical_list Error: %s", str(error))

    return result_list



def get_valid_pKa_pKb_data(data_dict_list):
    """
    Filter a list of dictionaries for valid 'pKa' and 'pKb' data.

    Args:
    - data_dict_list (list): List of dictionaries to be filtered

    Returns:
    - valid_dict_list (list): List containing dictionaries with valid 'pKa' or 'pKb' data
    """

    valid_dict_list = []
    invalid_dict_list =[]
    try:
        # Loop through each dictionary in the input list
        for data_dict in data_dict_list:
            valid_dict = {}
            invalid_dict ={}

            # Loop through key-value pairs in the dictionary
            for key, value in data_dict.items():

                # Check if the key is 'pKa'
                if key.lower() == "pka":

                    # Use regex to find valid 'pKa' pattern in the value
                    pka_match = re.search(r'(?<![a-zA-Z0-9])pKa(?![a-zA-Z0-9])', value)

                    if pka_match:
                        valid_dict["pKa"] = value

                    else:
                        invalid_dict["pKa"] = value

                # Check if the key is 'pKb'
                elif key.lower() == "pkb":

                    # Use regex to find valid 'pKb' pattern in the value
                    pkb_match = re.search(r'(?<![a-zA-Z0-9])pKb(?![a-zA-Z0-9])', value)
                    if pkb_match:
                        valid_dict["pKb"] = value
                    else:
                        invalid_dict["pKa"] = value
                else:
                    # For other keys, add them as-is to the valid dictionary
                    valid_dict[key] = value

            # If valid dictionary is not empty, append it to the list
            if len(valid_dict) != 0:
                valid_dict_list.append(valid_dict)

            # If invalid dictionary is not empty, append it to the list
            if len(invalid_dict) != 0:
                invalid_dict_list.append(invalid_dict)

        return valid_dict_list, invalid_dict_list

    except Exception as error:
        # Log an error if an exception occurs
        logging.error("get_valid_pKa_pKb_data Error: %s", str(error))

    return [],[]



def remove_nontable_dict_from_table_list(data_list, ignore_list):
    """
    Remove non-table dictionaries from a list of dictionaries based on specific criteria.

    Args:
    - data_list (list): List of dictionaries containing key-value pairs.

    Returns:
    - list: Filtered list containing dictionaries that meet the criteria.
    """
    try:
        # Function to check if the value in the dictionary meets the criteria
        def check_row(row):

            for value in row.values():
                value = value.replace("\n\n", "\n").replace("-\n", "").replace("\n", " ").replace("  ", " ")
                if value.startswith("Table"):

                    words = value.split()

                    if len(words) > 2:
                        second_word = words[1].replace("\n", " ").replace("  ", " ")
                        third_word = words[2].replace("\n", " ").replace("  ", " ") if len(words) > 2 else ' '

                        if len(second_word) > 5 and (second_word.lower() == \
                                            "continued" or second_word.lower() == "(continued)"):
                            return True
                        elif len(third_word) > 5 and (third_word.lower() ==\
                                             "continued" or third_word.lower() == "(continued)"):
                            return True
                        elif "pka" in second_word.lower() or "pka" in third_word.lower():
                            return True
                        elif not re.match(r"^[a-z]", third_word):
                            return True

                        elif len(second_word) > 5:
                            return True
                        elif second_word.lower() =="of":
                            return True
                        else:
                            return False
            return True

        # Filter rows based on the criteria using the check_row function
        filtered_rows = []
        ignored_rows = []

        for row in data_list:
            if check_row(row):
                filtered_rows.append(row)
            else:
                ignored_rows.append(row)

        for row in ignore_list:
            if not check_row(row):
                ignored_rows.append(row)

        ## remove table with crystallographic data  for "density" property
        valid_list, invalid_list = remove_density_crystal_dict(filtered_rows, ignored_rows)

        return valid_list, invalid_list

    except Exception as error:
        # Log the error
        logging.exception("An error occurred in remove_nontable_dict_from_table_list function: %s", str(error))

        # Raise the exception for higher-level handling
        # raise error("An error occurred in remove_nontable_dict_from_table_list function")


def remove_density_crystal_dict(input_list, invalid_list):
    """
    Categorize dictionaries into valid and invalid based on specific key conditions.

    Args:
    - input_list (list): List of dictionaries.
    - invalid_list (list): List to append invalid dictionaries.

    Returns:
    - tuple: Two lists of dictionaries - valid_list and invalid_list.
    """
    try:
        valid_list = []

        # Define the key and value conditions
        key_conditions = ["d20", "ρ", "𝜌", "sp. volume", "specific volume", "density", "densities", "mass density"]
        value_conditions = ["crystallographic", "crystal data and structure", "crystal data", "Crystal", "Crystal and refinement", "Spin density", "electron density", "apparent density","wood density"]

        for data_dict in input_list:

            # Iterate over each key-value pair in the dictionary
            for key, value in data_dict.items():
                # Clean up the value by removing unnecessary newlines and extra spaces
                cleaned_value = value.replace("\n\n", "\n").replace("-\n", "") \
                    .replace("\n", " ").replace("  ", " ")

                # Check if any key from key_conditions is a substring of the lowercase key
                if any(cond.lower() in key.lower() for cond in key_conditions):
                    # Check if any value condition matches the cleaned value
                    if any(item.lower() in cleaned_value.lower() for item in value_conditions):
                        # If match is found, mark it as invalid
                        # print(f"Invalid Entry Detected: {key}: {cleaned_value}")
                        if data_dict not in invalid_list:
                            invalid_list.append(data_dict)
                        break
                    else:
                        if data_dict not in valid_list:
                            valid_list.append(data_dict)
                        break
                else:
                    # If no match for key conditions, add to valid list
                    if data_dict not in valid_list:
                        valid_list.append(data_dict)
                    break

        return valid_list, invalid_list
    except Exception as error:
        # Log the error
        logging.exception("An error occurred in remove_density_crystal_dict function: %s", str(error))
    return [], []


def filter_table_sentences(text):
    """Get sentences from text starting with 'table'

    Args:
        text (str): sentence string

    Returns:
        bool: True if get sentence with "table" at start of line else False
    """
    try:
        pattern = r'(Table|TABLE|T\s*A\s*B\s*L\s*E|T\s*a\s*b\s*l\s*e)\s+(\d+)\s+([A-Z])'
        match = re.search(pattern, text)

        if match:
            return True
        return False

    except Exception as error:
        # Log an error if an exception occurs
        logging.error("filter_table_sentences Error: %s", str(error))



def check_properties_in_table(table_data, property_excel_file):
    """
    Check for properties in table data by matching them with properties listed in the Excel file.

    Args:
        table_data (str): Text data of a table extracted from a PDF.
        property_excel_file (str): Path to the Excel file containing properties.

    Returns:
        list: List of dictionaries containing properties found in the table data.
    """
    try:
        # print(f"{table_data = }")
        table_dict_list = []
        table_ignored_dict_list = []

        # Read the property names from the Excel file
        df_excel = pd.read_excel(property_excel_file)

        property_list = df_excel["property_name"].astype(str).tolist()

        # Iterate over each property in the property_list
        for prop in property_list:
            if len(table_data) !=0:
                if len(prop) < 4:

                    table_data1 = table_data.replace("-\n","").replace("\n"," ").replace("fﬁ", "ffi").replace("ﬂ","fl").\
                    replace("ﬀ","ff").replace("ﬃ","ffi").replace("ﬄ","ffl").replace("ʩ","fn")\
                    .replace("æ","ae").replace("&","et").replace("�", " ").replace("#"," ")\
                    .replace("C", "°C").replace("�C", "°C").replace("°С","°C" ).replace("   "," ").replace("  "," ")

                    if re.search(fr'(?<![a-zA-Z0-9:])\b{re.escape(prop)}(?![a-zA-Z])', table_data1, re.IGNORECASE):
                        table_dict_list.append({prop: table_data})

                    if any(substring in table_data1.lower() for substring in ["[α]d","[α]d20","[a]d","[a]d20"]):
                        table_dict_list.append({"[a]D": table_data})


                else:
                    table_data1 = table_data.replace("-\n","").replace("\n"," ").replace("fﬁ", "ffi").replace("ﬂ","fl").\
                    replace("ﬀ","ff").replace("ﬃ","ffi").replace("ﬄ","ffl").replace("ʩ","fn")\
                    .replace("æ","ae").replace("&","et").replace("�", " ").replace("#"," ")\
                    .replace("C", "°C").replace("�C", "°C").replace("°С","°C" ).replace("   "," ").replace("  "," ")
                    # # For longer properties, no need to check for specific conditions

                    # Search for each variation in table_data
                    if prop.lower() in table_data1.lower():
                        # print(f"1.{prop = }")
                        variations = [prop, prop + r' ', prop + r'\d{,2} ',\
                                       prop + r'[a-zA-Z]{,2} ', prop + r'[a-zA-Z0-9]{,2} ']
                        for variation in variations:

                            if re.search(variation, table_data1, re.IGNORECASE):
                                # print(f"2.{prop = }")
                                table_dict_list.append({prop: table_data})
                                break
        # print(f"{table_dict_list = }")
        # Apply ignore_dictionary_for_property function for property to the collected dictionaries
        filtered_table_dict_list = []
        for table_dict in table_dict_list:

            filtered_dict, ignored_dict = ignore_dictionary_for_property(table_dict, property_excel_file)
            # print(f">>{ignored_dict = }")
            if filtered_dict:

                filtered_table_dict_list.append(filtered_dict)

            if ignored_dict:
                table_ignored_dict_list.append(ignored_dict)
        # print(f">>{table_ignored_dict_list = }")
        return filtered_table_dict_list,table_ignored_dict_list

    except Exception as error:
        # Log an error if an exception occurs
        logging.error("check_properties_in_table Error: %s", str(error))

    return [],[]



def filter_by_keywords(table_dict):
    """
    Filter a dictionary based on specific keywords and ignore lists.

    Args:
    - table_dict (dict): Dictionary containing keys and values.

    Returns:
    - dict: Filtered dictionary excluding specified keywords.

    Raises:
    - Logging error if an exception occurs.
    """
    try:
        # Define lists of keywords and phrases to be ignored for specific keys
        key_checklist = ["yield", "solubility","solubilities", "concentration", 'm', 'e', 'χe', 'xe','x1']

        conc_ignore_list = ["minimum inhibitory concentration", "inhibitory concentration",
             "minimum concentration", "ligand", "ic50", "polymerization","protein"] #"MIC", "polymer",

        yield_ignore_list = ["formation", "solvent", "product", "reaction", "conversion",
                             "absorption", "adsorption", "catalyst", "selectivity", "synthesis",
                             "base", "reagent", "ligand", "polymer", "homopolymer", "copolymer",
                             "polymerization", "mol", "monomer", "monomers", "mol/l"]

        # Initialize an empty dictionary to store filtered key-value pairs
        filtered_dict = {}

        # Initialize an empty dictionary to store ignored key-value pairs
        ignored_dict = {}

        # Iterate through each key-value pair in table_dict
        for key, value in table_dict.items():

            # Flag to determine if the key-value pair should be ignored
            ignore = False

            # Check if the key is in the key_checklist
            if key.lower() in key_checklist:
                # print(f"1.{key =}")

                # Condition for specific keys like "yield", "e", or "ey"
                if key.lower() in ["yield", "e" , "ey"]:

                    # Iterate through yield_ignore_list to match ignore items in the value
                    for ignore_item in yield_ignore_list:

                        # Create a regex pattern to match the ignore_item and ignore its occurrences in the value
                        pattern = re.compile(r'\b{}\b'.format(re.escape(ignore_item)), re.IGNORECASE)

                        # Clean the value to be compared, ignoring specific characters and converting to lowercase
                        value_clean = value.replace("-\n", "").replace("\n", " ").\
                            replace("\\n", " ").replace("  ", " ").lower()

                        # Check if the pattern matches any part of the cleaned value; set ignore flag to True if found
                        if re.search(pattern, value_clean):
                            ignore = True

                            # Break the loop if an ignore_item is found in the value
                            break

                # Condition for other specific keys like "concentration", "x", "χ", "χ1", "solubility", "χe", "xe"
                elif key.lower() in ["concentration", "x", "χ", "χ1", "solubility","solubilities", "χe", "xe"]:

                    # Iterate through conc_ignore_list to match ignore items in the value
                    for ignore_item in conc_ignore_list:

                        # Create a regex pattern to match the ignore_item and ignore its occurrences in the value
                        pattern = re.compile(r'\b{}\b'.format(re.escape(ignore_item)), re.IGNORECASE)

                        # Check if the pattern matches any part of the value; set ignore flag to True if found
                        if re.search(pattern, value):
                            ignore = True

                            # Break the loop if an ignore_item is found in the value
                            break
            else:
                filtered_dict[key] = value

            # If the ignore flag is False, add the key-value pair to the filtered_dict
            if not ignore:
                filtered_dict[key] = value

            if ignore:
                ignored_dict[key] = value
        # print(f"{filtered_dict =}")
        return filtered_dict, ignored_dict

    except Exception as error:
        # Log an error if an exception occurs
        logging.error("filter_by_keywords Error: %s", str(error))

    return {},{}




def filter_by_prop_units(table_dict, df):
    """
    Filter a dictionary based on units present in the values and property
      units from a DataFrame.

    Args:
    - table_dict (dict): Dictionary containing keys and values.
    - df (DataFrame): DataFrame containing properties and their unit lists.

    Returns:
    - Tuple[dict, dict]: Filtered dictionary based on units present in the values,
      and an ignored dictionary for invalid key-value pairs.

    Raises:
    - Exception: If an error occurs during the filtering process.
    """
    try:
        df['property_name'] = df['property_name'].astype(str).str.lower()

        filtered_dict = {}
        ignored_dict = {}
        ignore = False
        if len(table_dict) != 0:

            for key, value in table_dict.items():
                prop_name = key.lower()
                added_to_filtered =False
                table_data_string = value.replace("ﬃ", "ffi").replace("fﬁ", "ffi").replace("ﬂ","fl")\
                        .replace("ﬀ","ff").replace("ﬃ","ffi").replace("ﬄ","ffl").replace("ʩ","fn")\
                        .replace("æ","ae").replace("&","et").replace("�", " ").replace("-\n","")\
                        .replace("\n", " ").replace("�"," ").replace("  ", " ").replace("C", "°C")\
                        .replace("\x01C", "°C").lower()

                if prop_name in table_data_string:
                    # print(f">>{prop_name =}")
                    unit_list = df.loc[df['property_name'] == prop_name, 'unit_list'].values[0]
                    # print(f">>{unit_list =}")
                    if isinstance(unit_list, float) and math.isnan(unit_list):
                        unit_list = ""
                        filtered_dict[key] = value
                        added_to_filtered = True
                        break

                    elif len(prop_name) <= 3 and prop_name not in ["nD", "nd" ]:

                        unit_list = [unit.strip() for unit in unit_list.split(',')] # type: ignore

                        symbols = [
                                "μ", "€", "£", "¥", "$", "#", "α", "β", "γ", "δ", "ε", "ζ", "η", "θ", "ι", "κ", "λ", "μ", "ν",
                                "ξ", "ο", "π", "ρ", "σ", "τ", "υ", "φ", "χ", "ψ", "ω", "∆", "∇", "∈", "∉", "∋", "∌", "∑", "∏",
                                "∫", "∬", "∭", "∮", "∯", "∰", "∇", "∂", "∀", "∁", "∃", "∄", "∅", "∆", "∇", "∈", "∉", "∊",
                                "∋", "∌", "∍", "∎", "∏", "∐", "∑", "−", "∓", "∔", "∕", "∖", "∗", "∘", "∙", "√", "∛", "∜",
                                "∝", "∞", "∟", "∠", "∡", "∢", "∣", "∤", "∥", "∦", "∧", "∨", "∩", "∪", "∫", "∬", "∭", "∮",
                                "∯", "∰", "∱", "∲", "∳", "∴", "∵", "∶", "∷", "∸", "∹", "∺", "∻", "∼", "∽", "∾", "∿", "≀",
                                "≁", "≂", "≃", "≄", "≅", "≆", "≇", "≈", "≉", "≊", "≋", "≌", "≍", "≎", "≏", "≐", "≑", "≒",
                                "≓", "≔", "≕", "≖", "≗", "≘", "≙", "≚", "≛", "≜", "≝", "≞", "≟", "≠", "≡", "≢", "≣", "≤",
                                "≥", "≦", "≧", "≨", "≩", "≪", "≫", "≬", "≭", "≮", "≯", "≰", "≱", "≲", "≳", "≴", "≵", "≶",
                                "≷", "≸", "≹", "≺", "≻", "≼", "≽", "≾", "≿", "⊀", "⊁", "⊂", "⊃", "⊄", "⊅", "⊆", "⊇", "⊈",
                                "⊉", "⊊", "⊋", "⊌", "⊍", "⊎", "⊏", "⊐", "⊑", "⊒", "⊓", "⊔", "⊕", "⊖", "⊗", "⊘", "⊙",
                                "⊚", "⊛", "⊜", "⊝", "⊞", "⊟", "⊠", "⊡", "⊢", "⊣", "⊤", "⊥", "⊦", "⊧", "⊨", "⊩", "⊪",
                                "⊫", "⊬", "⊭", "⊮", "⊯", "⊰", "⊱", "⊲", "⊳", "⊴", "⊵", "⊶", "⊷", "⊸", "⊹", "⊺", "⊻",
                                "⊼", "⊽", "⊾", "⊿", "⋀", "⋁", "⋂", "⋃", "⋄", "⋅", "⋆", "⋇", "⋈", "⋉", "⋊", "⋋", "⋌",
                                "⋍", "⋎", "⋏", "⋐", "⋑", "⋒", "⋓", "⋔", "⋕", "⋖", "⋗", "⋘", "⋙", "⋚", "⋛", "⋜", "⋝",
                                "⋞", "⋟", "⋠", "⋡", "⋢", "⋣", "⋤", "⋥", "⋦", "⋧", "⋨", "⋩", "⋪", "⋫", "⋬", "⋭", "⋮",
                                "⋯", "⋰", "⋱", "⋲", "⋳", "⋴", "⋵", "⋶", "⋷", "⋸", "⋹", "⋺", "⋻", "⋼", "⋽", "⋾", "⋿"
                            ]


                        symbol_pattern = ''.join(map(re.escape, symbols))
                        # prop_pattern = re.compile(fr'(?<![0-9a-zA-Z{symbol_pattern}])\
                        #         [.,\s()\[\]{{}}!@#$%^&*_+-]*{re.escape(prop_name)}(?:,\d{{1}})?(?!\.)\b', re.IGNORECASE)
                        prop_pattern = re.compile(fr'(?<![0-9a-zA-Z{symbol_pattern}])[.,\s()\[\]{{}}!@#$%^&*_+-]*{re.escape(prop_name)}(,\d{{1}})?(?!\.)\b', re.IGNORECASE)

                        # print(table_data_string)
                        if not re.search(prop_pattern, table_data_string):
                            # print(f"{key =}")
                            ignored_dict[key] = value
                            continue

                        for unit in sorted(unit_list, key=len, reverse=True):

                            try:
                                if len(unit) == 0:
                                    filtered_dict[key] = value
                                    added_to_filtered = True
                                    break

                                unit = re.sub(r'[,.\s()\[\]{}!ÃÂ¡�?@#$^&*�•º_=+⁄/–−∙-]', '', unit)
                                table_data_string_clean =re.sub(r'[;:,.\s()\[\]{}·!ÃÂ¡�?@#$^&*�•º~_=+⁄/–−∙-]', '', table_data_string)
                                # print(f"{prop_name =}")
                                if unit.lower() in table_data_string_clean.lower():
                                # if re.search(rf'\b(?:{re.escape(unit)})\b', table_data_string_clean, re.IGNORECASE):  ## CORRECTION V4
                                    # print(f"{prop_name =} : {unit =}")
                                    filtered_dict[key] = value
                                    added_to_filtered = True
                                    break

                            except Exception as error:
                                pass

                        # If not added to filtered_dict, then add to ignored_dict
                        if not added_to_filtered:
                            ignored_dict[key] = value

                    else:
                        table_data_string_clean = table_data_string.replace("fﬁ", "ffi").replace("ﬂ","fl").\
                            replace("ﬀ","ff").replace("ﬃ","ffi").replace("ﬄ","ffl").replace("ʩ","fn").\
                            replace("æ","ae").replace("&","et").replace("�", " ").replace("C", "°C").replace("  ", " ")

                        magnetic_moment_list = ["B.M.","BM","magnetic dipole moment","magnetic moment","leff","µeff"]
                        solubility_list = ["solubility","solubility","χ", "χ1","concentration","χe","x1","solubilities"]
                        if not re.search(prop_name, table_data_string_clean) and prop_name.lower() not in ["[α]d","[α]d","[α]d20","[a]d","[a]d20","[α]d20"]:
                            ignored_dict[key] = value
                            continue

                        elif prop_name in ["δhr°", "δsr°"]:
                            filtered_dict[key] = value
                            added_to_filtered = True

                        elif prop_name.lower() in ["[α]d","[α]d20","[a]d","[a]d20"]:
                            filtered_dict[key] = value
                            added_to_filtered = True

                        elif prop_name.lower() in magnetic_moment_list:
                            filtered_dict[key] = value
                            added_to_filtered = True

                        elif prop_name.lower() in solubility_list:
                            if "103x" in table_data_string_clean.lower() or "1000x" in table_data_string_clean.lower():
                                filtered_dict[key] = value
                                added_to_filtered = True

                        else:
                            if prop_name.lower() in table_data_string_clean.lower():
                                filtered_dict[key] = value
                                added_to_filtered = True

                        unit_list = [unit.strip() for unit in unit_list.split(',')] # type: ignore
                        for unit in sorted(unit_list, key=len, reverse=True):
                            try:
                                if len(unit) == 0:
                                    if key.lower() in ["pka", "pkb", "dielectric constant","refractive index","n",\
                                         "Ɛ", "εr", "Ɛs","εmax", "di-electric constant","dielectric permittivity"  ]:
                                        filtered_dict[key] = value
                                        added_to_filtered = True
                                        break

                                    else:
                                        ignored_dict[key] = value
                                        break

                                unit = re.sub(r'[:;,.\s()\[\]{}!ÃÂ¡�?@#$^&*•º_=+⁄/–∙-]', '', unit)
                                table_data_string_clean = re.sub(r'[:;,.\s()\[\]{}·!ÃÂ¡�?@#$^&*•º~_=+⁄/–∙-]', '', table_data_string_clean)  #/

                                # if unit.lower() in table_data_string_clean:
                                if re.search(rf'\b(?:{re.escape(unit)})\b', table_data_string_clean, re.IGNORECASE):  ## CORRECTION V4
                                    filtered_dict[key] = value
                                    added_to_filtered = True
                                    break

                            except Exception as error:
                                # print(f"{error =}")
                                pass

                        # If not added to filtered_dict, then add to ignored_dict
                        if not added_to_filtered:
                            ignored_dict[key] = value

            cleaned_ignored_dict = {}
            for k, v in ignored_dict.items():
                found_duplicate = False

                for key, value in filtered_dict.items():
                    if k.lower() == key.lower() and v[:50].lower() == value[:50].lower():
                        found_duplicate = True
                        print(f"FOUND DUPLICATE {k  :<40} : {v}")
                        break

                if not found_duplicate:
                    cleaned_ignored_dict[k] = v

            ignored_dict = cleaned_ignored_dict
        # print(f"{filtered_dict = }")
        return filtered_dict, ignored_dict

    except Exception as error:
        logging.error("filter_by_prop_units Error: %s", str(error))

    # Return empty dictionaries in case of any exception
    return {}, {}



def ignore_dictionary_for_property(table_dict, property_excel_file):
    """
    Ignore properties in a dictionary based on specific criteria defined in the
      property Excel file.

    Args:
    - table_dict (dict): Dictionary containing keys and values.
    - property_excel_file (str): File path of the Excel file containing property names.

    Returns:
    - Tuple[dict, dict]: Final filtered dictionary after ignoring specific properties
      and ignored dictionary.

    Raises:
    - Exception: If an error occurs during the processing.
    """

    try:
        # Read the Excel file into a DataFrame
        df = pd.read_excel(property_excel_file)

        final_ignored_dict = {}

        # Convert the 'property_name' column to lowercase for case-insensitive comparison
        df['property_name'] = df['property_name'].astype(str).str.lower()

        # Filter the dictionary based on predefined keywords
        try:
            filtered_dict1, ignored_dict1 = filter_by_keywords(table_dict)

            if filtered_dict1 is not None and ignored_dict1 is not None:
                final_ignored_dict.update(ignored_dict1)
        except Exception as e:
            logging.error("Error in filter_by_keywords: %s", str(e))
            filtered_dict1, ignored_dict1 = None, None
        # print(f"{ignored_dict1 =}")

        # Filter the dictionary further based on units present in the values
        try:
            final_dict, ignored_dict = filter_by_prop_units(filtered_dict1, df)
            # print(f"{ignored_dict =}")
            if ignored_dict is not None:
                final_ignored_dict.update(ignored_dict)
        except Exception as e:
            logging.error("Error in filter_by_prop_units: %s", str(e))
            final_dict, ignored_dict = {}, {}
        # print(f"{final_ignored_dict =}")
        # Return the final filtered dictionary and ignored dictionary
        return final_dict, final_ignored_dict


    except Exception as error:
        # Log an error if an exception occurs
        logging.error("ignore_dictionary_for_property Error: %s", str(error))

    # Return empty dictionaries if an error occurs
    return {}, {}



def add_property_group_column(curation_df, property_synonym_file):
    """
    Add the 'property_group' column to the given DataFrame based on property synonyms.

    This function reads property synonyms from an Excel file and matches them with
    properties in the provided DataFrame. It then adds the corresponding 'property_group'
    values to the DataFrame.

    Args:
        curation_df  pd.DataFrame): The DataFrame containing the curation data.
        property_synonym_file (str): Path to the Excel file containing property synonyms.

    Returns:
     pd.DataFrame: The input DataFrame with the 'property_group' column added.
    """
    try:
        # Initialize the 'property_group' column
        curation_df['property_name'] = ""

        # Read property synonyms from Excel file
        property_synonym_df = pd.read_excel(property_synonym_file)

        # Clean the property names in the DataFrame
        curation_df['property'] = curation_df['property'].str.strip().str.lower()

        # Clean the property names in the Excel file
        property_synonym_df['property_synonyms'] = property_synonym_df['property_synonyms']\
                                                       .str.strip().str.lower()

        # Create a dictionary of property synonyms
        property_synonyms = {}
        for index, row in property_synonym_df.iterrows():

            synonyms = [synonym.strip().lower() for synonym in row['property_synonyms'].split(',')]
            property_synonyms[row['property_name'].strip().lower()] = synonyms

        # Match properties and assign property groups
        unmatched_properties = set()
        for index, row_curation in curation_df.iterrows():
            cur_property = row_curation['property']

            # Check if the current property matches any synonym or the exact property name
            matched = False
            for property_name, synonyms in property_synonyms.items():

                if cur_property in synonyms or cur_property == property_name:

                    # Assign the property group to the 'property_group' column
                    curation_df.at[index, 'property_name'] = property_name
                    matched = True

                    # Break the loop after assigning the property group
                    break

            if not matched:
                unmatched_properties.add(cur_property)

        print("Unmatched properties:", unmatched_properties)
        return curation_df

    except Exception as error:
        logging.error("add_property_group_column Error: %s", str(error))
        return pd.DataFrame()
# #####################################################################################
# ############################### END OF CODE #########################################
# #####################################################################################




if __name__ == "__main__":
    start_time = time.time()
    logging.basicConfig(filename ='table_data_json.log', level=logging.ERROR,\
                         format = '%(asctime)s - %(levelname)s: %(message)s')

    property_excel_file = r"property_unit_data\property_units.xlsx"

    property_synonym_file = r"property_unit_data\property_groups.xlsx"
    abbreviation_full_form_excel = r"property_unit_data\abbreviations_excel.xlsx"

    # Path to table_data.json file
    json_file_path = r"extracted_data_folder\41405412A.article.003\table_data.json"

    # Extract tables with properties from JSON instead of PDF
    table_data_list, table_ignored_data_list = extract_tables_with_properties_from_json(
        json_file_path, property_excel_file, abbreviation_full_form_excel
    )

    print(f"{json_file_path =}")
    print(f"{len(table_data_list) =}")
    print(f"{len(table_ignored_data_list) =}")

    # Debug: Check if JSON file is being read correctly
    debug_table_data = read_table_data_json(json_file_path)
    print(f"Debug: Number of tables read from JSON: {len(debug_table_data)}")
    if debug_table_data:
        print(f"Debug: First table title: {debug_table_data[0].get('title', 'No title')}")
        print(f"Debug: First table has {len(debug_table_data[0].get('table_data', []))} rows")

###############################################################################
    # Get the final property table dictionary list in the expected format
    prop_table_dict_list = get_property_table_dict_list_from_json(
        json_file_path, property_excel_file, abbreviation_full_form_excel
    )

    print("************* EXPECTED OUTPUT FORMAT ********************")
    print("prop_table_dict_list = [")
    for i, table_dict in enumerate(prop_table_dict_list):
        if i < len(prop_table_dict_list) - 1:
            print(f"    {table_dict},")
        else:
            print(f"    {table_dict}")
    print("]")
    print(f"Total number of property tables found: {len(prop_table_dict_list)}")

###############################################################################
    max_sentence_length = 200

    # Note: For JSON-based processing, we'll use the JSON file path for calculated table checks
    # The check_calculated_table_in_dict_values function may need to be updated for JSON processing
    final_valid_data, final_invalid_data, final_calculated_list = check_calculated_table_in_dict_values(json_file_path,\
                     max_sentence_length ,table_data_list, table_ignored_data_list)

    print("************* Final Valid DATA ********************")
    print(f"{len(final_valid_data) =}")
    for table_data in final_valid_data:
        print(table_data)
        # for key, value in table_data.items():
            # print(f"valid Property = {key}.")
        print("-" * 50)

    print("************* Final IGNORED DATA ******************")

    print(f"{len(final_invalid_data) = }")
    for ignored_data in final_invalid_data:
        # print(ignored_data)
        for key, value in ignored_data.items():
            print(f"Invalid Property = {key}.")
        print("=" * 50)
    # print(f"Total time required is {round((time.time() - start_time),2)} seconds")

    print("*************** Final CALCULATED DATA **************")

    print(f"{len(final_calculated_list) = }")
    for calculated_data in final_calculated_list:
        # print(calculated_data)
        for key, value in calculated_data.items():
            print(f"calculated data Property = {key}.")
        print("=" * 50)
    print(f"Total time required is {round((time.time() - start_time),2)} seconds")
#########################################################################
############################### END OF CODE #############################
#########################################################################
