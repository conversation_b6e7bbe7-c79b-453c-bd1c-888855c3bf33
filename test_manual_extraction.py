"""
Test script to verify manual extraction fixes for Young modulus data.
"""

from get_property_data_from_table_by_llama3_v4 import (
    manual_json_parse,
    fix_comma_delimiter_issues,
    fix_quotes_and_commas_aggressively,
    validate_extracted_data,
    extract_data_to_dataframe
)

# Simulate problematic LLaMA 3.2 response with comma delimiter issues
problematic_response = '''[
    {"property_name": "Young modulus", "value": "2.16 ± 0.122", "unit": "GPa" "compound": "P(BA-a)"},
    {"property_name": "Young modulus", "value": "2.56 ± 0.191", "unit": "GPa", "compound": "P(BA-a/AC1)"}
    {"property_name": "Young modulus", "value": "2.84 ± 0.182", "unit": "GPa", "compound": "P(BA-a/AC2)"}
]'''

# Another problematic response
problematic_response2 = '''[
    {"property_name": "Young modulus" "value": "2.16 ± 0.122" "unit": "GPa" "compound": "P(BA-a)"}
    {"property_name": "Young modulus" "value": "2.56 ± 0.191" "unit": "GPa" "compound": "P(BA-a/AC1)"}
]'''

def test_comma_fixing():
    """Test comma delimiter fixing functions"""
    print("=" * 60)
    print("TESTING COMMA DELIMITER FIXES")
    print("=" * 60)
    
    print("Original problematic response:")
    print(problematic_response)
    
    # Test comma fixing
    fixed_response = fix_comma_delimiter_issues(problematic_response)
    print(f"\nAfter comma fixing:")
    print(fixed_response)
    
    # Test aggressive fixing
    aggressive_fixed = fix_quotes_and_commas_aggressively(problematic_response)
    print(f"\nAfter aggressive fixing:")
    print(aggressive_fixed)
    
    return fixed_response

def test_manual_extraction():
    """Test manual extraction with problematic responses"""
    print("\n" + "=" * 60)
    print("TESTING MANUAL EXTRACTION")
    print("=" * 60)
    
    # Test with first problematic response
    print("Testing manual extraction on problematic response 1:")
    result1 = manual_json_parse(problematic_response, "Young modulus")
    print(f"Extracted {len(result1)} items")
    
    # Test with second problematic response
    print("\nTesting manual extraction on problematic response 2:")
    result2 = manual_json_parse(problematic_response2, "Young modulus")
    print(f"Extracted {len(result2)} items")
    
    return result1 + result2

def test_validation_and_dataframe():
    """Test validation and DataFrame creation"""
    print("\n" + "=" * 60)
    print("TESTING VALIDATION AND DATAFRAME CREATION")
    print("=" * 60)
    
    # Simulate manually extracted data
    manual_data = [
        {"property_name": "Young modulus", "value": "2.16 ± 0.122", "unit": "GPa", "compound": "P(BA-a)"},
        {"property_name": "Young modulus", "value": "2.56 ± 0.191", "unit": "GPa", "compound": "P(BA-a/AC1)"},
        {"property_name": "Young modulus", "value": "2.84 ± 0.182", "unit": "GPa", "compound": "P(BA-a/AC2)"}
    ]
    
    # Create response dict
    response = {"Young modulus": manual_data}
    
    # Simulate table metadata
    prop_table_dict = {
        "Young modulus": {
            "table_data": [
                {"0": "Sample", "1": "Tensile Stress", "2": "(MPa)", "3": "Young modulus (GPa)"},
                {"0": "P(BA-a)", "1": "27.93 ± 0.78", "2": "27.93 ± 0.78", "3": "2.16 ± 0.122"},
                {"0": "P(BA-a/AC1)", "1": "32.49 ± 1.21", "2": "32.49 ± 1.21", "3": "2.56 ± 0.191"}
            ]
        }
    }
    
    table_metadata = {
        "Young modulus": {
            "table_id": "table_4_2",
            "page_number": 4,
            "coordinates": {"left": 42, "top": 100, "right": 562, "bottom": 200, "coord_origin": "TOPLEFT"},
            "page_size": {"width": 595, "height": 793}
        }
    }
    
    print("Original response data:")
    for key, data in response.items():
        print(f"  {key}: {len(data)} items")
    
    # Test validation
    validated = validate_extracted_data(response, prop_table_dict)
    print(f"\nAfter validation:")
    for key, data in validated.items():
        print(f"  {key}: {len(data)} items")
    
    # Test DataFrame creation
    df = extract_data_to_dataframe(validated, table_metadata)
    print(f"\nDataFrame created:")
    print(f"  Shape: {df.shape}")
    print(f"  Columns: {list(df.columns)}")
    
    if not df.empty:
        print(f"\nDataFrame content:")
        print(df[['property_name', 'value', 'unit', 'compound']].to_string())
    
    return df

def test_end_to_end():
    """Test complete end-to-end flow"""
    print("\n" + "=" * 60)
    print("TESTING END-TO-END FLOW")
    print("=" * 60)
    
    # Start with problematic response
    print("1. Starting with problematic LLaMA response...")
    
    # Try to fix it
    print("2. Attempting to fix comma issues...")
    try:
        import json
        fixed = fix_comma_delimiter_issues(problematic_response)
        parsed = json.loads(fixed)
        print(f"   ✅ Successfully parsed {len(parsed)} items")
        return parsed
    except:
        print("   ❌ Comma fixing failed, trying manual extraction...")
        
        # Fall back to manual extraction
        manual_result = manual_json_parse(problematic_response, "Young modulus")
        print(f"   ✅ Manual extraction found {len(manual_result)} items")
        return manual_result

if __name__ == "__main__":
    print("TESTING FIXES FOR YOUNG MODULUS EXTRACTION ISSUES")
    print("=" * 60)
    
    # Run all tests
    fixed_response = test_comma_fixing()
    manual_results = test_manual_extraction()
    df = test_validation_and_dataframe()
    end_to_end_result = test_end_to_end()
    
    print("\n" + "=" * 60)
    print("SUMMARY OF FIXES")
    print("=" * 60)
    print("✅ Enhanced comma delimiter fixing")
    print("✅ Improved manual extraction with multiple strategies")
    print("✅ Relaxed validation to keep manually extracted data")
    print("✅ Added debugging output to track data flow")
    print("✅ Better error handling for LLaMA 3.2 responses")
    
    print(f"\nExpected result: Young modulus data should now appear in final DataFrame")
    print(f"Manual extraction should find data even when JSON parsing fails")
