"""
This script is used to extract the sentences from JSON table data
 containing table patterns and also to extract the table number.

This module will extract sentences containing any of the calculated
 terms from the given list along with table numbers if present.

Updated to work specifically with JSON table data for identifying calculated/predicted tables.

Date: 06-06-2025
@author: <PERSON>ha<PERSON>
"""

## Import libraries
import re
import logging
import json



## List of keywords to find sentences containing it.
calculated_list  = [ "calculated","calculating","calculation","calculations",
                     "predicted", "prediction","predicting","predict","predictions",
                     "estimated","estimating","estimate","estimation","estimations"
                     "simulated","simulating","simulate",
                     "molecular dynamics","molecular simulation","molecular simulations", "molecule simulation", "dft", "ab initio","calculate",]  #

experimented_list = ["experimentation","experimented","experimental","experiment","experiments"]



def convert_json_table_to_string(table_entry):
    """
    Convert JSON table entry to string format for analysis.

    Args:
        table_entry (dict): JSON table entry with title and table_data

    Returns:
        str: String representation of the table data
    """
    try:
        if isinstance(table_entry, str):
            # Already a string, return as is
            return table_entry

        if isinstance(table_entry, dict):
            # JSON table entry
            title = table_entry.get('title', '')
            table_data_rows = table_entry.get('table_data', [])

            # Create string representation
            string_parts = [title]

            for row in table_data_rows:
                row_values = []
                for key in sorted(row.keys(), key=lambda x: int(x) if x.isdigit() else float('inf')):
                    row_values.append(str(row[key]))
                string_parts.append(' '.join(row_values))

            return '\n'.join(string_parts)

        return str(table_entry)

    except Exception as error:
        logging.error(f"convert_json_table_to_string Error: {str(error)}")
        return str(table_entry)



def get_table_line(line):
    """ Get sentence from line if it is a table line

    Args:
        line (_str_): Sentence from a pdf file

    Returns:
        line (_str_): Sentence from a pdf file if it is a table line
    """
    # Check if the line matches the table pattern regex
    table_pattern = re.compile(r'\b(Table|TABLE|T\s+A\s+B\s+L\s+E|T\s+a\s+b\s+l\s+e)\s+(\d+[A-Za-z]?|\w+|[IVXLCDM]+)(?::|\.|)\s*.*$', re.IGNORECASE)

    # Check if the line matches the table pattern regex
    if table_pattern.search(line):
        return line
    else:
        return None



def extract_table_sentences_from_json(json_path, max_line_length=200):
    """Extract table sentences from JSON table data

    Args:
        json_path (_str_): Path to the JSON file containing table data
        max_line_length (_int_): Maximum length of a line

    Returns:
        table_sentences (_list_): List of table sentences
    """

    table_sentences = []

    try:
        # Read JSON file
        with open(json_path, 'r', encoding='utf-8') as file:
            data = json.load(file)

        # Extract table information from JSON
        if 'tables' in data:
            for table_id, table_info in data['tables'].items():
                title = table_info.get('title', '')

                # Check if title contains table pattern and add to sentences
                if title and len(title) <= max_line_length:
                    # Clean the title
                    title_clean = title.replace("-\n", "").replace("\n\n", "\n").replace("\n", " ").replace("  ", " ")

                    # Check if the cleaned title is a table line
                    tab_line = get_table_line(title_clean)

                    # Check if the line is a table line
                    if tab_line is not None:
                        table_sentences.append(tab_line)

        return table_sentences

    except Exception as error:
        logging.exception(f"Error in extract_table_sentences_from_json: {error}")
        return table_sentences



def remove_nontable_string(table_sentence):
    """Ignore nontable string from input sentence

    Args:
        table_sentence (_str_): Sentence from a pdf file containing table pattern

    Returns:
        _bool_: True if sentence is a table line else False
    """
    # Check if the second word starts with a number  or Roman numeral with starting uppercase letter.
    if table_sentence.startswith("Table") or table_sentence.startswith("TABLE"):

        #Split the sentence on space
        words = table_sentence.split()

        if len(words) >= 2:
            second_word = words[1].replace("\n", " ").replace("  ", " ")\
                  if len(words) >= 2 else ' '
            if not re.match(r"^[a-z]", second_word):
                return True
            else:
                return False



def get_table_numbers(table_lines):
    """Extract table numbers from table lines

    Args:
        table_lines (_list_): List of table lines

    Returns:
        table_numbers (_list_): List of table numbers
    """

    table_numbers = []

    #Get table pattern matches
    pattern = re.compile(r'(Table|TABLE|T A B L E|T a b l e)\.?\s*(\w+)')

    #Iterate through matches and extract numbers
    for line in table_lines:
        line = line.replace("T A B L E", "TABLE").\
            replace( "T a b l e", "Table").replace("  "," ")
        match = re.search(pattern, line)

        if match:
            table_num = match.group()
            parts = table_num.split()

            # Extract table number
            if len(parts) > 1 and len(parts[0]) ==5 and len(parts[1]) <= 4 :
                if parts[1][0].isupper() or parts[1].isdigit() or parts[1].isalnum():

                    if any(substring in line.lower() for substring in \
                            calculated_list) \
                        and not any(substring in line.lower() for substring in\
                            experimented_list):
                        # print(f"{line}")
                        is_table = remove_nontable_string(table_num)
                        if is_table:
                            table_numbers.append(table_num)

    # print(f"{table_numbers = }")
    return table_numbers



def check_calculated_table_in_dict_values(json_file_path, max_line_length,
                            valid_table_dict_list, invalid_table_dict_list):
    """Check if calculated table is present in the value of the dictionary.
    Updated to work with JSON table data instead of PDF.

    Args:
        json_file_path (_str_): Path to the JSON file containing table data
        max_line_length (_int_): Maximum length of a line
        valid_table_dict_list (_list_): List of valid table dictionary (with JSON table data)
        invalid_table_dict_list (_list_): List of invalid table dictionary

    Returns:
        final_valid_list (_list_): List of valid table dictionary
        final_invalid_list (_list_): List of invalid table dictionary
        final_calculated_list (_list_): List of calculated table dictionary

    """
    try:
        final_valid_list = []
        final_calculated_list = []

        # Extract table sentences from the JSON file
        table_lines = extract_table_sentences_from_json(json_file_path, max_line_length)
        # print(f"{table_lines =}")

        # Extract table numbers from the table lines
        table_num_list = get_table_numbers(table_lines)

        # Iterating through valid table dictionaries
        for table_dict in valid_table_dict_list:
            is_valid = True

            # Iterate through dictionary values (now JSON table entries)
            for property_name, table_entry in table_dict.items():

                # Convert JSON table entry to string for analysis
                value_string = convert_json_table_to_string(table_entry)

                # Check if any calculated table numbers are mentioned in the first 30 characters
                if any(item.lower() in value_string[:30].lower() for item in table_num_list):
                    # print(f"calculated table number found in value for property {property_name}")
                    is_valid = False
                    break

                # Check for calculated/predicted keywords in first 200 characters
                elif any(substring in value_string[:200].lower() for substring in calculated_list) \
                    and not any(substring in value_string.lower() for substring in experimented_list):
                    # print(f"calculated keywords found in value for property {property_name}")
                    is_valid = False
                    break
                else:
                    # Continue checking other properties in this table
                    continue

            # Categorize the table based on analysis
            if is_valid:
                if table_dict not in final_valid_list:
                    final_valid_list.append(table_dict)
            else:
                if table_dict not in final_calculated_list:
                    final_calculated_list.append(table_dict)

        return final_valid_list, invalid_table_dict_list, final_calculated_list

    except Exception as error:
        logging.exception(f"check_calculated_table_in_dict_values Error: {str(error)}")
        return valid_table_dict_list, invalid_table_dict_list, []
# ######################################################################################





if __name__ == "__main__":
    # shipment_path = r"\\***************\cas_app_files\PDF_Highlighting\pdf_hig_e1_240229"

    # pdf_file = "41322265U.article.002.pdf"    ## Calculated

    # pdf_file_path = shipment_path + "\\" + pdf_file

    pdf_file_path = r"pdf_folder\41322265U.article.002.pdf"

    # valid_table_dict_list = [
    #     {"viscosity" :"Table 4/n The number of atoms in the crystal"},
    #     {"density": "Table 5/n The number of atoms in the crystal"}]

    # invalid_table_dict_list =[
    #     {"Pressure" :"Table 6/n The number of atoms in the crystal"},
    #     {"Tempertaure": "Table 7/n The number of atoms in the crystal"}]


    valid_table_dict_list = []

    invalid_table_dict_list = []


    max_sentence_length = 200

    valid_list , invalid_list, calculated_list = check_calculated_table_in_dict_values(pdf_file_path, max_sentence_length, valid_table_dict_list, invalid_table_dict_list)

    print(f"valid_list: {valid_list}")
    print("=====================================================")
    print(f"invalid_list: {invalid_list}")
    print("=====================================================")
    print(f"invalid_list: {calculated_list}")
# ######################################################################################
